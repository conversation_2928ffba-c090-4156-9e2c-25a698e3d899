@use '../../utils' as *;

/*----------------------------------------*/
/*  offcanvas
/*----------------------------------------*/
.edoc-offcanvas {
  position: fixed;
  z-index: 999;
  background: #fff;
  backdrop-filter: blur(5px);
  width: 250px;
  left: 0;
  top: 0;
  padding: 20px 20px;
  height: 100vh;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
  transition: 0.3s ease-in-out;

  &-open {
    visibility: visible;
    opacity: 1;
    transform: translateX(0);
  }

  &-wrapper {
    position: relative;

    .edoc-offcanvas-close {
      position: absolute;
      top: 0;
      inset-inline-end: 0;
    }

    .edoc-offcanvas-navbars {
      >ul {
        list-style-type: none;

        >li {
          margin-bottom: 15px;

          @media #{$xs} {
            margin-bottom: 8px;
          }

          &:last-child {
            margin-bottom: 0;
          }

          >a {
            color: var(--edc-heading);
            font-size: 12px;
            font-weight: 500;
            line-height: lh(28, 14);
            text-transform: uppercase;
            transition: all 0.3s ease-in-out;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &:hover{
              color: var(--edc-primary);
            }

            &.active{
              position: relative;
              transition: all 0.3s ease-in-out;

              &::after{
                content: '';
                position: absolute;
                bottom: 0;
                inset-inline-start: 0;
                width: 100%;
                height: 1px;
                background-color: var(--td-heading);
              }
            }

            .down-icon {
              color: var(--td-heading);
              margin-inline-end: 5px;
              display: flex;
            }
          }

          ul {
            list-style-type: none;
            margin-top: 8px;
            display: none;

            li {
              a {
                font-size: 14px;
                color: var(--td-text-primary);
              }
            }
          }
        }
      }
    }

    .header-top-content{
      margin-top: 20px;

      .header-top-right-card{
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;

        &:last-child{
          margin-bottom: 0;
        }

        .icon{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 30px;
          height: 30px;
          border-radius: 5px;
          background-color: var(--edc-primary);

          .header-top-icon{
            color: var(--td-white);
            font-size: 18px;
          }
        }
        .text{
          h6{
            font-size: 14px;
            font-weight: 500;
            color: var(--edc-heading);
          }
          p{
            font-size: 12px;
            font-weight: 400;
            color: var(--edc-text-primary);
          }
        }
      }
    }

    .appointment-btn{
      margin-top: 26px;
    }
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .edoc-offcanvas-logo {
      display: inline-block;
      height: 24px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  &-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 998;
    width: 100%;
    height: 100%;
    visibility: hidden;
    opacity: 0;
    transition: 0.45s ease-in-out;
    background: rgba(24, 24, 24, 0.4);

    &-open {
      visibility: visible;
      opacity: 0.7;
    }
  }
}


.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}

.edoc-offcanvas-close{
  .edoc-offcanvas-close-toggle{
    display: flex;
    justify-content: center;
    align-items: center;
    transform: translateY(1px);

    .cancel-icon{
      font-size: 22px;
      color: var(--edc-heading);
    }
  }
}