@use "../../../utils" as *;

/*----------------------------------------*/
/*  Hero
/*----------------------------------------*/
.edoc-hero {
  background: linear-gradient(90deg, rgba(7, 187, 168, 0.1) -21.65%, rgba(7, 187, 168, 0) 59.96%);
  margin-top: 24px;
  position: relative;

  .hero-bg-element {
    clip-path: polygon(0.426% 17.006%,
        0.426% 17.006%,
        0.107% 14.501%,
        0.064% 12.031%,
        0.275% 9.65%,
        0.721% 7.412%,
        1.378% 5.371%,
        2.227% 3.581%,
        3.246% 2.095%,
        4.414% 0.967%,
        5.71% 0.251%,
        7.113% 0%,
        100% 0%,
        100% 100%,
        20.86% 100%,
        20.86% 100%,
        19.964% 99.896%,
        19.095% 99.592%,
        18.264% 99.098%,
        17.478% 98.425%,
        16.745% 97.584%,
        16.074% 96.586%,
        15.473% 95.443%,
        14.951% 94.163%,
        14.515% 92.76%,
        14.174% 91.243%,
        0.426% 17.006%);
    background: var(--edc-primary);
    width: 943px;
    height: 503px;
    position: absolute;
    top: 165px;
    right: 0;
    z-index: 2;

    @media #{$xxl,$xl,$lg,$md,$xs} {
      width: 820px;
    }

    @media #{$xl,$lg,$md,$xs} {
      width: 665px;
    }

    @media #{$lg,$md,$xs} {
      width: 540px;
      height: 370px;
    }

    @media #{$md,$xs} {
      display: none;
    }
  }

  .bg-shape-element {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .rectangle-shape {
    position: absolute;
    bottom: 0;
    right: 68px;
    z-index: 3;

    @media #{$lg,$md,$xs} {
      display: none;
    }
  }

  .elem-1 {
    position: absolute;
    top: 160px;
    right: 22px;
    z-index: 3;

    @media #{$lg,$md,$xs} {
      top: 167px;
      right: 290px;
    }
  }

  .elem-2 {
    position: absolute;
    top: 192px;
    right: 700px;
    z-index: 3;

    @media #{$xxl,$xl,$lg,$md,$xs} {
      top: 175px;
      right: 580px;
    }
  }

  .elem-3 {
    position: absolute;
    top: 30px;
    right: 800px;
    z-index: 3;

    @media #{$lg,$md,$xs} {
      top: 20px;
      right: 500px;
    }

    @media #{$md,$xs} {
      display: none;
    }
  }

  .elem-4 {
    position: absolute;
    top: 65px;
    right: 280px;
    z-index: 3;

    @media #{$xxl,$xl,$lg,$md,$xs} {
      right: 420px;
    }

    @media #{$lg} {
      right: 320px;
    }

    @media #{$md,$xs} {
      display: none;
    }
  }

  .stats-card {
    padding: 16px;
    border-radius: 5px;
    background: var(--td-white);
    box-shadow: 0px 12px 56px 0px rgba(6, 28, 61, 0.12);
    position: absolute;
    left: 51%;
    top: 272px;
    z-index: 6;

    @media #{$xxl,$xl,$lg,$md,$xs} {
      left: 42%;
    }

    @media #{$lg} {
      left: 47%;
      top: 290px;
      padding: 10px;
    }

    @media #{$md} {
      left: 15%;
      top: 320px;
    }

    @media #{$xs} {
      left: 8%;
      top: 320px;
      display: none;
    }

    @media #{$sm} {
      left: 8%;
      top: 320px;
      display: block;
    }

    .icon {
      display: flex;
      width: 50px;
      height: 50px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      background: var(--edc-primary);
      border-radius: 8px;
      margin-bottom: 20px;

      @media #{$lg,$md,$xs} {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
      }

      img {
        width: 30px;
        height: 30px;

        @media #{$lg,$md,$xs} {
          width: 20px;
          height: 20px;
        }
      }
    }

    h5 {
      color: var(--edc-heading);
      font-size: 24px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 4px;
    }

    h6 {
      color: var(--edc-text-primary);
      font-size: 16px;
      font-weight: 300;
      line-height: normal;
      margin-bottom: 10px;
    }

    .star-box {
      display: flex;
      align-items: center;
      gap: 6px;

      p {
        color: var(--edc-text-primary);
        font-size: 14px;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .doctors-card {
    padding: 20px;
    border-radius: 12px;
    background: var(--td-white);
    position: absolute;
    bottom: 36px;
    right: 12%;
    z-index: 6;

    @media #{$lg,$md,$xs} {
      padding: 10px;
    }

    h5 {
      color: var(--edc-heading);
      font-size: 14px;
      font-weight: 500;
      line-height: lh(20, 14);
      letter-spacing: -0.168px;
      margin-bottom: 12px;
    }

    .doctors {
      display: flex;
      align-items: center;

      .doctor {
        width: 39px;
        height: 39px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: -13px;
        border: 1px solid var(--td-white);

        @media #{$lg,$md,$xs} {
          width: 30px;
          height: 30px;
        }

        &:last-child {
          margin-right: 0;
        }

        .counter {
          width: 39px;
          height: 39px;
          background-color: var(--edc-primary);
          color: var(--td-white);
          text-align: center;
          font-size: 14px;
          font-weight: 700;
          line-height: lh(14, 14);
          display: flex;
          justify-content: center;
          align-items: center;

          @media #{$lg,$md,$xs} {
            width: 30px;
            height: 30px;
            font-size: 12px;
            font-weight: 500;
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  .edoc-hero-content {
    position: relative;
    z-index: 5;

    .left {

      @media #{$md} {
        margin-top: 30px;
      }

      @media #{$xs} {
        margin-top: 5px;
      }

      @media #{$sm} {
        margin-top: 30px; 
      }

      .edoc-website-title {
        margin-bottom: 16px;

        @media #{$xxl,$xl,$lg} {
          width: 90%;
        }

        @media #{$md,$xs} {
          width: 100%;
        }
      }

      .edoc-website-subtitle {
        @media #{$xl,$lg,$md,$xs} {
          width: 70%;
        }

        @media #{$xs} {
          width: 90%;
        }

        @media #{$sm} {
          width: 70%;
        }
      }

      .edoc-action-btn {
        margin-top: 40px;

        @media #{$md,$xs} {
          margin-top: 25px;
        }
      }
    }

    .right {
      position: relative;

      @media #{$md,$xs} {
        margin-top: -40px;
      }

      @media #{$xs} {
        margin-top: -20px;
      }

      @media #{$sm} {
        margin-top: -40px;
      }

      .hero-img {
        height: 624px;
        margin-top: 20px;
        display: flex;
        justify-content: end;

        @media #{$lg,$md,$xs} {
          height: 491px;
        }

        @media #{$md,$xs} {
          z-index: 5;
          position: relative;
          justify-content: center;
          height: 424px;
        }

        @media #{$xs} {
          height: 300px;
        }

        @media #{$sm} {
          height: 424px;
        }

        img {
          height: 100%;
        }
      }

      .hero-bg-element-2 {
        background: var(--edc-primary);
        width: 100%;
        height: 503px;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
        bottom: 0;
        left: 0;
        z-index: 2;
        transform: rotate(180deg);
        border-radius: 0 0 20px 20px;

        @media #{$xxl,$xl,$lg,$md,$xs} {
          width: 820px;
        }

        @media #{$xl,$lg,$md,$xs} {
          width: 665px;
        }

        @media #{$lg,$md,$xs} {
          width: 100%;
          height: 270px;
        }

        @media #{$xs} {
          height: 170px;
        }

        @media #{$sm} {
          height: 270px;
        }
      }
    }
  }
}