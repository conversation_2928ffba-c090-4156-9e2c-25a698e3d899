// ==================================================
// * Project Name   : Multipurpose Theme || We are dedicated to being your website live.
// * File           :  JS Base
// * Version        :  1.0
// * Last change    :  May 2025, Saturday
// * Author         :  tdevs (https://codecanyon.net/user/tdevs/portfolio)
// ==================================================

(function ($) {
  'use strict';

  var windowOn = $(window);
  // preloader 
  $(window).on('load', function () {
    $('#preloader').fadeOut(500);
  });

  // back-to-top
  var btn = $('#back-to-top');
  windowOn.scroll(function () {
    if (windowOn.scrollTop() > 300) {
      btn.addClass('show');
    } else {
      btn.removeClass('show');
    }
  });
  btn.on('click', function () {
    $('html, body').animate({ scrollTop: 0 }, 0);
  });

  //mobile menu
  $(".edoc-offcanvas-toggle").on('click', function () {
    $(".edoc-offcanvas").addClass("edoc-offcanvas-open");
    $(".edoc-offcanvas-overlay").addClass("edoc-offcanvas-overlay-open");
  });
  $(".edoc-offcanvas-close-toggle,.edoc-offcanvas-overlay").on('click', function () {
    $(".edoc-offcanvas").removeClass("edoc-offcanvas-open");
    $(".edoc-offcanvas-overlay").removeClass("edoc-offcanvas-overlay-open");
  });

  // progress bar animation when viewport came
  $(document).ready(function () {
    function isInViewport(element) {
      var rect = element.getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
    }

    function handleScroll() {
      $('.edc-progressbar').each(function () {
        if (isInViewport(this) && !$(this).hasClass('animated')) {
          $(this).addClass('animated');

          var width = $(this).attr('style').match(/width:(\d+)%/)[1];
          var progressValue = $(this).find('.edc-progressvalue').text();
          var isPercentage = progressValue.indexOf('%') !== -1;
          var targetValue = isPercentage ? width : progressValue;

          $(this).css('width', '0%');
          if (isPercentage) {
            $(this).find('.edc-progressvalue').text('0%');
          }

          var progressBar = $(this);
          var currentValue = 0;
          var animation = setInterval(function () {
            currentValue += 1;
            if (currentValue <= width) {
              progressBar.css('width', currentValue + '%');
              if (isPercentage) {
                progressBar.find('.edc-progressvalue').text(currentValue + '%');
              }
            } else {
              if (!isPercentage) {
                progressBar.find('.edc-progressvalue').text(targetValue);
              }
              clearInterval(animation);
            }
          }, 2000 / width);
        }
      });
    }
    handleScroll();

    $(window).on('scroll', handleScroll);
  });

  //nice select all
  $(document).ready(function () {
    $('.nice-select-sort-1').niceSelect();
  });
  $(document).ready(function () {
    $('.nice-select-sort-2').niceSelect();
  });

  // faq functionality
  $(document).ready(function() {
    // Hide all answer boxes initially except the active one
    $('.faq-full-box .answer-box').not('.active').hide();
    
    $('.faq-full-box .question-box').click(function() {
      const faqBox = $(this).closest('.faq-full-box');
      const answerBox = faqBox.find('.answer-box');
      const plusIcon = faqBox.find('.plus');
      
      const isActive = answerBox.hasClass('active');
      
      $('.faq-full-box .answer-box').not(answerBox).removeClass('active').slideUp(300);
      $('.faq-full-box .plus').not(plusIcon).removeClass('active');
      
      if (isActive) {
        answerBox.removeClass('active').slideUp(300);
        plusIcon.removeClass('active');
      } else {
        answerBox.addClass('active').slideDown(300);
        plusIcon.addClass('active');
      }
    });
});

//testimonial swiper
var swiper = new Swiper(".myEdocTestimonialSwiper", {
  navigation: {
    nextEl: ".edc-slider-prev-next",
    prevEl: ".edc-slider-prev-btn",
  },
});









})(jQuery);