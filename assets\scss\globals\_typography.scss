@use "../utils" as u;
@use "../utils" as *;

/*----------------------------------------*/
/*   1.3 typography
/*----------------------------------------*/
* {
	margin: 0;
	padding: 0;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

body {
	&.edoccare {
		font-family: var(--edc-body-font);
		font-size: 16px;
		font-weight: normal;
		line-height: 1.5;
	}
}

h1,
h2,
h3,
h4,
h5,
h6 {
	color: var(--td-heading);
	margin-top: 0px;
	line-height: 1.3;
	margin-bottom: 0;
	word-break: break-word;
}

p {
	margin-bottom: 0;
}


a {
	text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
	@include td-transition;
}

a:focus,
.button:focus {
	text-decoration: none;
	outline: none;
}

a:focus,
a:hover {
	text-decoration: none;
	color: inherit;
}

a,
button {
	color: inherit;
	outline: none;
	border: none;
	background: transparent;
}

.o-x-clip {
	overflow-x: clip;
}

ul {
	margin-bottom: 0;
	margin-left: 0;
	padding-left: 0;
	list-style-type: none;
}

img {
	max-width: 100%;
	object-fit: cover;
}

button {
	font-family: var(--td-ff-body) !important;
}

button:hover {
	cursor: pointer;
}

button:focus {
	outline: 0;
	font-family: var(--td-ff-body);
}

.uppercase {
	text-transform: uppercase;
}

.capitalize {
	text-transform: capitalize;
}

hr:not([size]) {
	border-color: var(--td-card-bg-1);
	opacity: 1;
	border-width: 1px;
}

*::-moz-selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

::-moz-selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

::selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

*::-moz-placeholder {
	opacity: 1;
	font-size: 14px;
}

*::placeholder {
	opacity: 1;
	font-size: 14px;
	font-weight: 400;
}

/*---------------------------------
  1.2 Common Classes
---------------------------------*/
.w-img {
	& img {
		width: 100%;
	}
}

.m-img {
	& img {
		max-width: 100%;
	}
}

.fix {
	overflow: hidden;
}

.clear {
	clear: both;
}

.f-left {
	float: left;
}

.f-right {
	float: right;
}

.z-index-1 {
	z-index: 1;
}

.z-index-11 {
	z-index: 11;
}

.p-relative {
	position: relative;
}

.p-absolute {
	position: absolute;
}

.position-absolute {
	position: absolute;
}

.include-bg {
	background-position: center;
	background-size: cover;
	background-repeat: no-repeat;
}

.hr-1 {
	border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
	overflow-x: clip;
}

.o-visible {
	overflow: visible;
}

.valign {
	@include flexbox();
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
}