@use '../../../utils' as *;

/*----------------------------------------*/
/*  Blog details
/*----------------------------------------*/
.blog-details {
  .left {
    margin-right: 20px;

    @media #{$lg,$md,$xs} {
      margin-right: 0px;
    }

    .blog-details-banner {
      width: 100%;
      height: 438px;

      @media #{$lg} {
        height: 380px;
      }

      @media #{$md} {
        height: 350px;
      }

      @media #{$xs} {
        height: 250px;
      }

      @media #{$sm} {
        height: 300px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 16px;
      }
    }

    .blog-amenities {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-top: 16px;

      @media #{$xs} {
        flex-direction: column;
        align-items: start;
      }


      .post-by {
        display: flex;
        align-items: center;
        gap: 8px;

        .icon {
          display: flex;

          .post-icon {
            font-size: 16px;
            color: var(--edc-text-primary);
          }
        }

        p {
          color: var(--edc-text-primary);
          font-size: 14px;
          font-weight: 400;
          line-height: normal;
        }
      }
    }

    .blog-des {
      margin-top: 30px;

      h2 {
        color: var(--edc-heading);
        font-size: 30px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 16px;

        @media #{$xs} {
          font-size: 26px;
        }

      }

      p {
        color: var(--edc-text-primary);
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
      }

      h3 {
        color: var(--edc-heading);
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: lh(26, 20);
        margin-top: 20px;
      }

      ul {
        margin-top: 10px;
        margin-bottom: 10px;

        li {
          color: var(--edc-text-primary);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: lh(26, 16);
          margin-bottom: 5px;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            color: var(--edc-heading);
            font-weight: 500;
          }
        }
      }

    }

    hr {
      border-color: #093E63;
      opacity: 0.1;
      border-width: 1px;
    }

    .share {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;

      h5 {
        color: var(--edc-heading);
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: lh(26, 20);
      }

      .social-links {
        display: flex;
        align-items: center;
        gap: 10px;

        a {
          display: inline-flex;
          width: 40px;
          height: 40px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 50%;
          border: 1px solid var(--edc-text-primary);
          transition: all 0.3s ease-in-out;

          .social-icon {
            font-size: 20px;
            color: var(--edc-text-primary);
          }

          &:hover {
            border: 1px solid var(--edc-heading);
            background: var(--edc-heading);

            .social-icon {
              color: var(--td-white);
            }
          }
        }
      }
    }
  }

  .right {
    .right-card {
      padding: 24px;
      border-radius: 24px;
      background: var(--edc-heading);
      margin-bottom: 48px;

      @media #{$lg,$md,$xs} {
        margin-bottom: 20px;
      }

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        padding: 16px;
        border-radius: 8px;
        border: 1px solid rgba(7, 187, 168, 0.04);
        background: rgba(255, 255, 255, 0.04);
        color: var(--td-white);
        font-size: 20px;
        font-weight: 600;
        line-height: lh(26, 20);
        margin-bottom: 16px;
      }

      ul {
        li {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          a {
            display: flex;
            align-items: center;
            gap: 8px;
            color: rgba(255, 255, 255, 0.60);
            font-size: 16px;
            font-weight: 500;
            line-height: lh(28, 16);
            transition: all 0.3s ease-in-out;

            .icon {
              display: flex;
              align-items: center;
              justify-content: center;

              .check-icon {
                color: rgba(255, 255, 255, 0.60);
                font-size: 24px;
              }
            }

            &:hover {
              color: var(--td-white);

              .icon {

                .check-icon {
                  color: var(--td-white);
                }
              }

            }
          }
        }
      }
    }
  }
}