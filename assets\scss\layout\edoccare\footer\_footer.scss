@use '../../../utils' as *;

/*----------------------------------------*/
/*  5.1 footer
/*----------------------------------------*/
.edoc-footer {
  background-color: var(--edc-heading);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding-top: 100px;
  padding-bottom: 30px;
  position: relative;

  @media #{$lg} {
    padding-top: 80px;
    padding-bottom: 25px;
  }

  @media #{$md} {
    padding-top: 70px;
    padding-bottom: 20px;
  }

  @media #{$xs} {
    padding-top: 50px;
    padding-bottom: 16px;
  }

  @media #{$sm} {
    padding-top: 70px;
    padding-bottom: 20px;
  }

  .edoc-footer-top {
    margin-bottom: 80px;

    @media #{$md} {
      margin-bottom: 50px;
    }

    @media #{$xs} {
      margin-bottom: 40px;
    }

    .edoc-foot-1 {
      @media #{$xs} {
        margin-bottom: 10px;
      }

      .foot-logo {
        display: inline-block;
        height: 30px;
        margin-bottom: 16px;

        img {
          height: 100%;
        }
      }

      .foot-des {
        color: var(--edc-text-primary);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(26, 16);
        margin-bottom: 40px;
        width: 78%;

        @media #{$lg,$md,$xs} {
          width: 100%;
        }

        @media #{$md,$xs} {
          margin-bottom: 20px;
        }
      }

      .links {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-bottom: 20px;

        @media #{$md,$xs} {
          margin-bottom: 10px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .social-link {
          font-size: 20px;
          color: var(--edc-text-primary);
        }

        p {
          color: var(--edc-text-primary);
          font-size: 16px;
          font-weight: 400;
          line-height: normal;
        }
      }
    }

    .edoc-foot-4 {
      margin-left: 80px;

      @media #{$md} {
        margin-left: 0px;
        margin-top: 50px;
      }

      @media #{$xs} {
        margin-left: 0px;
        margin-top: 10px;
      }
    }

    .footer-common-links {
      h4 {
        color: var(--td-white);
        font-size: 20px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 20px;
      }

      .footer-common-links-des {
        color: var(--edc-text-primary);
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
        width: 78%;

        @media #{$lg,$md,$xs} {
          width: 100%;
        }
      }

      ul {
        list-style-type: none;

        li {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          a {
            color: var(--edc-text-primary);
            font-size: 16px;
            font-weight: 400;
            line-height: normal;
            transition: all 0.3s ease-in-out;

            &:hover {
              color: var(--edc-primary);
            }
          }
        }
      }

      .phone-call-btn {
        display: flex;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid var(--edc-primary);
        margin-top: 30px;
        align-items: center;
        gap: 16px;

        .icon {
          display: inline-flex;
          width: 30px;
          height: 30px;
          justify-content: center;
          align-items: center;

          .call-icon {
            font-size: 30px;
            color: var(--td-white);
          }
        }

        .texts {
          display: inline-flex;
          flex-direction: column;

          .title {
            display: inline-block;
            color: var(--td-white);
            font-size: 18px;
            font-weight: 700;
            line-height: normal;
            margin-bottom: 8px;
          }

          .subtitle {
            display: inline-block;
            color: var(--td-white);
            font-size: 16px;
            font-weight: 500;
            line-height: normal;
          }
        }
      }
    }
  }

  .edoc-footer-bottom {
    padding-top: 14px;
    border-top: 1px solid rgba(107, 139, 161, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media #{$xs} {
      flex-direction: column;
      justify-content: start;
      align-items: start;
    }

    @media #{$sm} {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .left {
      p {
        color: var(--edc-text-primary);
        font-size: 14px;
        font-weight: 400;
        line-height: normal;
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 16px;

      @media #{$xs} {
        margin-top: 10px;
      }

      @media #{$sm} {
        margin-top: 0px;
      }

      a {
        display: inline-flex;
        width: 40px;
        height: 40px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 50%;
        border: 1px solid var(--edc-text-primary);
        transition: all 0.3s ease-in-out;

        @media #{$xs} {
          width: 30px;
          height: 30px;
        }

        @media #{$sm} {
          width: 40px;
          height: 40px;
        }

        .social-icon {
          font-size: 20px;
          color: var(--edc-text-primary);

          @media #{$xs} {
            font-size: 16px;
          }

          @media #{$sm} {
            font-size: 20px;
          }
        }

        &:hover {
          background-color: var(--edc-text-primary);

          .social-icon {
            color: var(--td-white);
          }
        }
      }
    }
  }

  .edoc-footer-element {
    position: absolute;
    bottom: 0;
    right: 0;

    @media #{$xl,$lg,$md,$xs} {
      display: none;
    }
  }
}