@use "../../utils" as *;

/*----------------------------------------*/
/*  forms
/*----------------------------------------*/
// Customize form
input[type="text"],
input[type="search"],
input[type="email"],
input[type="tel"],
input[type="number"],
input[type="password"],
textarea {
  outline: none;
  height: 52px;
  width: 100%;
  padding: 0 16px;
  border-radius: 8px;
  border: 1px solid rgba(34, 34, 34, 0.10);
  color: rgba(8, 8, 8, 0.6);
  background: transparent;
  color: #093E63;
  font-size: 13px;
  font-weight: 400;
  line-height: normal;

  &::placeholder {
    color: rgba(9, 62, 99, 0.6);
  }

  &:focus {
    border-color: var(--edc-primary);
    box-shadow: unset;
    opacity: 1;
    border: 1px solid var(--edc-primary);
  }

  &:focus {
    border-color: var(--edc-primary);
    color: var(--ed-heading);
    background: transparent;

    &::placeholder {
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  &.input-exceptional {
    background: #f4f4f4;

    &-2 {
      height: 35px;
      background-color: var(--td-white);
    }
  }

  &.input-design-2 {
    background-color: transparent;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 0;
    color: var(--td-white);

    &:focus {
      border-color: var(--td-white);
      box-shadow: unset;
      opacity: 1;
      background-color: rgba(8, 8, 8, 0.2);
    }
  }

  &.input-design-pxNone {
    padding: 0 0;

    &:focus {
      background-color: rgba(8, 8, 8, 0);
    }
  }
}

textarea {
  padding: 14px 16px;
  height: 135px !important;
  color: var(--edc-heading);

  &::placeholder {
    color: rgba(9, 62, 99, 0.6);
  }

  &:focus {
    border-color: var(--edc-primary);
  }
}

// Custom Checkbox and radio button
input[type="checkbox"],
input[type="radio"] {
  opacity: 0;
  position: absolute;

  ~label {
    position: relative;
    font-size: 14px;
    line-height: line-height(25, 14);
    color: var(--td-text-primary);
    font-weight: 400;
    padding-inline-start: 20px;
    cursor: pointer;
    margin-bottom: 0;

    a {
      color: var(--td-primary);
      transition: all 0.3s ease-in-out;

      &:hover {
        color: var(--td-heading);
      }
    }

    &::before {
      content: " ";
      position: absolute;
      top: 3px;
      inset-inline-start: 0;
      width: 14px;
      height: 14px;
      background-color: var(--td-white);
      border: 1px solid #dbdbdb;
      border-radius: 4px;
      transition: all 0.3s;
    }

    &::after {
      content: " ";
      position: absolute;
      top: 7px;
      inset-inline-start: 2px;
      width: 10px;
      height: 5px;
      background-color: transparent;
      border-bottom: 1px solid var(--td-white);
      border-inset-inline-start: 1px solid var(--td-white);
      border-radius: 2px;
      // transform: rotate(-45deg);
      opacity: 0;
      transition: all 0.3s;
    }
  }

  &:checked {
    ~label {
      &::before {
        background-color: var(--td-white);
        border-color: #dbdbdb;
      }

      &::after {
        opacity: 1;
      }
    }
  }
}

input[type="radio"] {
  ~label {
    &::before {
      border-radius: 50%;
    }

    &::after {
      width: 6.1px;
      height: 7px;
      inset-inline-start: 4px;
      background: var(--td-primary);
      border-radius: 50%;
    }
  }
}

// animate custom check box
.animate-custom {
  .cbx {
    -webkit-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    cursor: pointer;

    span {
      display: inline-block;
      vertical-align: middle;

      a {
        color: var(--td-primary);

        &:hover {
          color: $black;
        }
      }

      &:first-child {
        position: relative;
        width: 18px;
        height: 18px;
        border-radius: 4px;
        transform: scale(1);
        vertical-align: middle;
        border: 1px solid #b9b8c3;
        transition: all 0.2s ease;

        svg {
          position: absolute;
          z-index: 1;
          top: 4px;
          inset-inline-start: 2px;
          fill: none;
          stroke: var(--td-white);
          stroke-width: 2;
          stroke-linecap: round;
          stroke-linejoin: round;
          stroke-dasharray: 16px;
          stroke-dashoffset: 16px;
          transition: all 0.3s ease;
          transition-delay: 0.1s;
          transform: translate3d(0, 0, 0);
        }

        &:before {
          content: "";
          width: 100%;
          height: 100%;
          background: var(--td-primary);
          display: block;
          transform: scale(0);
          opacity: 1;
          border-radius: 50%;
          transition-delay: 0.2s;
        }
      }

      &:last-child {
        margin-inset-inline-start: 6px;
        color: var(--td-text-primary);
        font-weight: 500;
        font-size: 14px;

        &:after {
          content: "";
          position: absolute;
          top: 8px;
          inset-inline-start: 0;
          height: 1px;
          width: 100%;
          background: #b9b8c3;
          transform-origin: 0 0;
          transform: scaleX(0);
        }
      }
    }

    &:hover {
      span {
        &:first-child {
          border-color: var(--td-primary);
        }
      }
    }
  }

  .inp-cbx {
    &:checked {
      &+.cbx {
        span {
          &:first-child {
            border-color: var(--td-primary);
            background: var(--td-primary);
            animation: check-15 0.6s ease;

            svg {
              stroke-dashoffset: 0;
            }

            &:before {
              transform: scale(2.2);
              opacity: 0;
              transition: all 0.6s ease;
            }
          }

          &:last-child {
            transition: all 0.3s ease;
          }
        }
      }
    }
  }

  input[type="checkbox"]~label::after {
    display: none;
  }

  input[type="checkbox"]~label {
    padding-inline-start: 0;
  }
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}

.td-form-group {
  .input-label {
    color: var(--edc-heading);
    font-size: 14px;
    font-weight: 400;
    line-height: normal;

    span {
      color: #EC0707;
    }
  }
}

// was-validated
.was-validated {
  .td-form-group {
    .input-field {
      position: relative;

      input {
        border-color: var(--td-danger);
        background: rgba(220, 29, 75, 0.1);

        &:focus {
          background: rgba(220, 29, 75, 0.1);
        }
      }
    }
  }
}

// single input style
.edc-form {
  .td-form-group {
    &.has-right-icon {
      .box-input {
        padding-inline-end: 50px;
      }

      .input-icon {
        position: absolute;
        inset-inline-end: 15px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;

        i {
          font-size: 14px;
        }

        &.eyeicon {
          cursor: pointer;
          inset-inline-end: 20px !important;
          inset-inline-start: auto !important;
        }

        &.icon-selected {
          svg * {
            stroke: rgba($heading, $alpha: 0.7);
            /* Change stroke color */
            fill: rgba($heading, $alpha: 0.7);
            /* Change stroke color */
            stroke-opacity: 1;
            /* Full opacity */
            transition: all 0.3s ease;
            /* Smooth animation */
          }
        }
      }
    }

    &.selected_icon {
      .input-icon {
        inset-inline-end: 33px;
        cursor: pointer;
      }
    }

    &.has-left-icon {
      .box-input {
        padding-inline-start: 45px;
      }

      // .input-field{
      // 	input{
      // 		padding: 0 45px 0 15px;
      // 	}
      // }

      .input-icon {
        position: absolute;
        inset-inline-start: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        width: max-content;

        &.eyeicon {
          cursor: pointer;
        }
      }
    }

    .input-field {
      position: relative;

      &.date-of-birth {
        position: relative;

        .icon {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          inset-inline-end: 15px;
        }
      }

      &.has-right-icon {
        position: relative;

        .form-control {
          color: #008080;
        }

        .icon {
          position: absolute;
          inset-inline-end: 15px;
          top: 50%;
          transform: translateY(-50%);
          background-color: #f8f9fa;

          .copy-icon {
            font-size: 14px;
            color: #6b7280;
          }

          .copy-tooltip {
            position: absolute;
            top: -30px;
            inset-inline-end: 0;
            background-color: #000;
            color: #fff;
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }

          &.show-tooltip .copy-tooltip {
            opacity: 1;
          }
        }
      }

      .edit-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        inset-inline-end: 15px;
        height: 20px;
        display: flex;
        padding: 2px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 4px;
        background: var(--td-card-bg-1);
        color: var(--td-white);
        font-size: rem(10);
        font-weight: 400;
        line-height: lh(16, 10);
      }

      &.input-group {
        flex-wrap: nowrap;
      }

      .input-group-text {
        color: var(--td-white);
        background: rgba($color: $white, $alpha: 0.08);
        mix-blend-mode: normal;
        border: 1px solid rgba($color: $white, $alpha: 0.08);
        @include border-radius(12px);
      }

      &.disabled {

        input,
        textarea {
          color: rgba($color: $white, $alpha: 0.5);
          cursor: not-allowed;

          &:focus {
            border-color: rgba($white, $alpha: 0.08);
          }
        }
      }

      .text-content {
        background: var(--td-white);
        box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
        border-radius: 5px;
        position: absolute;
        top: 50%;
        inset-inline-end: 5px;
        transform: translateY(-50%);
        padding: 5px 8px 6px;
        font-size: 14px;
        font-weight: 500;
        color: var(--td-primary);
      }

      input,
      textarea {
        font-size: 14px;
        letter-spacing: -0.03em;

        @include td-placeholder {
          color: rgba($color: $heading, $alpha: 0.65);
          font-size: 14px;
        }
      }

      textarea {
        padding: 12px 15px;
        height: 156px;
        resize: none;
        line-height: 1.5;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: transparent;
        color: var(--td-white);
        font-size: 14px;
        font-weight: 300;

        &::placeholder {
          color: #9DB2C1;
          font-size: 14px;
          font-weight: 300;
          line-height: 100%;
        }

        &:focus {
          border: 1px solid #8ba7bb;
        }
      }

      &.height-large {
        textarea {
          height: 237px;
        }
      }

      .form-control {
        height: 52px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: transparent;
        color: var(--td-white);
        font-size: 14px;
        font-weight: 300;
        line-height: 100%;

        &::placeholder {
          color: #9DB2C1;
          font-size: 14px;
          font-weight: 300;
          line-height: 100%;
        }

        &:focus {
          border: 1px solid #8ba7bb;
        }
      }

      &-icon {
        input {
          padding: 0 45px 0 15px;

          @include rtl {
            padding: 0 15px 0 45px;
          }
        }
      }

      &-exceptional {
        margin-top: 8px;
      }
    }

    .input-field-phone {
      position: relative;

      .form-control {
        padding: 0 15px 0 75px;
      }

      .country-code {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        inset-inline-start: 15px;
        padding-inline-end: 10px;
        border-inline-end: 1px solid #cacaca;
      }
    }

    .input-description {
      font-size: 12px;
      margin-top: 7px;
    }

    .input-label {
      color: #fff;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      display: flex;
      margin-bottom: 0.4em;

      span {
        padding-inline-start: 4px;
        display: flex;
        align-items: center;
        gap: 6px;
        color: #ec0707;
      }
    }

    .input-label-inner {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &>p {
        font-size: 12px;
      }
    }

    .input-select {
      .nice-select {
        height: 44px;
        width: 100%;
        padding: 0 15px;
        @include flexbox();
        align-items: center;
        float: none;
        border: 1px solid rgba($white, $alpha: 0.08);
        @include border-radius(12px);
        background-color: rgba($color: $white, $alpha: 0.08);

        .current {
          text-align: left;
          font-size: 14px;
          position: relative;
          color: var(--td-white);
        }

        .list {
          @include transform(scale(1) translateY(0));
          width: 100%;
          padding: 10px 0;
          @include border-radius(6px);
          background: #242424;
          border-radius: 12px;
          border-style: solid;
          border-color: rgba($white, $alpha: 0.08);
          border-width: 1px;
          padding: 12px 12px 12px 12px;
          max-height: 300px;
          overflow-y: scroll;
          -ms-overflow-style: none;
          /* IE and Edge */
          scrollbar-width: none;
          /* Firefox */
        }

        &::after {
          font-size: 16px;
          inset-inline-end: 16px;
          width: 8px;
          height: 8px;
          border-bottom: 1.5px solid var(--td-text-primary);
          border-inline-end: 1.5px solid var(--td-text-primary);
          font-size: 16px;
          content: "";
          position: absolute;
          top: 50%;
          transform: translateY(-50%) rotate(45deg);
          border: 5px solid;
          border-top-color: rgba(0, 0, 0, 0);
          border-left-color: rgba(0, 0, 0, 0);
          background-color: rgba(0, 0, 0, 0);
          transition: all ease-in-out 0.2s;
          margin-top: -2px;
          @include border-radius(2px);
        }

        .option {
          font-size: 14px;
          line-height: 38px;
          min-height: 38px;
          color: var(--td-white);
          border-radius: 10px;
          padding: 0 10px;

          &.selected {
            font-weight: 500;
          }

          &:hover {
            background-color: #353535;
          }

          &.selected.focus {
            background-color: #353535;
          }
        }

        &.open,
        &:focus {
          background-color: #353535;
        }
      }
    }

    &.input-fill {
      .input-label {
        font-weight: 700;
      }

      input,
      select,
      textarea {
        background-color: #fcfcfc;
        border: 1px solid rgba($heading, $alpha: 0.2);

        &:focus {
          border-color: var(--td-primary);
        }
      }
    }

    // form-select
    .form-select {
      height: 50px;
      border-radius: 8px;
      font-size: 14px;

      &:focus {
        font-size: 14px;
        // border: 1px solid var(--td-primary);
      }
    }

    .otp-verification {
      @include flexbox();
      gap: 10px 10px;
      flex-wrap: wrap;
      max-width: max-content;
      justify-content: center;
      margin: 0 auto;

      @media #{$xs,$sm,$md} {
        gap: 10px 10px;
      }

      input {
        background: rgba(103, 107, 113, 0.1);
        border: 1px solid rgba($white, $alpha: 0.1);
        width: 69.83px;
        height: 77px;
        text-align: center;
        font-size: 18px;
        font-weight: 500;

        @media #{$xs,$sm,$md} {
          height: 55px;
          width: 50px;
        }
      }
    }
  }
}

// feedback-invalid
.feedback-invalid {
  font-size: 12px;
  margin-top: 3px;
  color: #dc1d4b;
  display: none;

  &.active {
    display: block;
  }
}

.input-attention {
  font-size: 12px;
  color: var(--tdvar(--td-danger));

  &.xs {
    font-size: 10px;
  }
}

*::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

*::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

.common-select2-dropdown {
  .select2-container {
    width: 100% !important;

    &.select2-container--open {
      .select2-selection--single {
        border-radius: 20px 20px 0 0;
      }
    }
  }

  .select2-container .select2-selection--single {
    height: 45px;
    border-radius: 40px;
  }

  .select2-container--default .select2-selection--single {
    border: 1px solid #cacaca;
    background: #fff;
  }

  .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: rgba(8, 8, 8, 0.6);
    line-height: 43px;
    font-size: 14px;
    padding-inline-end: 35px;
    padding-inline-start: 14px;
  }

  .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 40px;
    position: absolute;
    top: 1px;
    inset-inline-end: 10px;
    width: 20px;
  }

  .select2-dropdown {
    background-color: var(--td-bg);
    border: 1px solid var(--td-card-bg-2);
    border-radius: 4px;
  }

  .select2-results__option {
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
    color: #fff;
    padding: 0 15px;
  }

  .select2-results__option {
    padding: 6px 15px;
    user-select: none;
    -webkit-user-select: none;
    font-size: 14px;
    color: #fff;
  }
}