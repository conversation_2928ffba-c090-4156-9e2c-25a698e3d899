@use '../../../utils' as *;

/*----------------------------------------*/
/*  Contact
/*----------------------------------------*/
.edoc-contact-page {
  .edoc-contact-page-content {
    position: relative;
    z-index: 5;

    .contact-details {
      .contact-details-card {
        padding: 30px;
        border-radius: 30px;
        border: 1px solid rgba(34, 34, 34, 0.10);
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .icon {
          display: flex;
          width: 60px;
          height: 60px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          background: var(--edc-primary);
          border-radius: 8px;
          margin-bottom: 20px;

          .location-icon {
            font-size: 30px;
            color: var(--td-white);
          }
        }

        .text {
          text-align: center;

          h6 {
            color: var(--edc-heading);
            font-size: 20px;
            font-weight: 500;
            line-height: normal;
            margin-bottom: 16px;
          }

          p {
            color: var(--edc-text-primary);
            text-align: center;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: lh(26, 16);
            margin-bottom: 40px;
          }

          a {
            color: var(--edc-primary);
            text-align: center;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            text-decoration-line: underline;
            text-decoration-style: solid;
            text-decoration-skip-ink: none;
            text-decoration-thickness: 6.5%;
            text-underline-offset: 14%;
            text-underline-position: from-font;
            transition: all 0.3s ease-in-out;

            &:hover {
              color: var(--edc-heading);
            }
          }
        }
      }
    }
  }
}

.contact-page-forms {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .contact-page-form-content {
    .left {
      h2 {
        color: var(--edc-heading);
        font-size: 48px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 16px;

        @media #{$xl} {
          font-size: 40px;
        }

        @media #{$lg} {
          font-size: 36px;
        }

        @media #{$md} {
          font-size: 30px;
        }

        @media #{$xs} {
          font-size: 26px;
        } 
      }

      p {
        color: var(--edc-text-primary);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(26, 16);
        margin-bottom: 50px;
      }

      .action-btn {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

.edoc-google-location {
  .iframe {
    iframe {
      width: 100%;
      height: 672px;
      border: none;

      @media #{$xl} {
        height: 600px;
      }

      @media #{$lg} {
        height: 550px;
      }

      @media #{$md} {
        height: 500px;
      }

      @media #{$xs} {
        height: 450px;
      }
    }
  }
}