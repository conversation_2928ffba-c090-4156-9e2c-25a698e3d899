@use '../../utils' as *;

/*----------------------------------------*/
/*  testimonial
/*----------------------------------------*/
.edoc-testimonial {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;

  .edoc-testimonial-slider-full {
    display: flex;
    justify-content: center;
    align-items: center;

    .edc-slider-prev-btn,
    .edc-slider-prev-next {
      display: flex;
      width: 50px;
      height: 50px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 8px;
      background: var(--edc-primary);
      border: 1px solid var(--edc-primary);
      flex-shrink: 0;

      @media #{$xs} {
        width: 30px;
        height: 30px;
      }

      @media #{$sm} {
        width: 40px;
        height: 40px;
      }

      .arrow-icon {
        font-size: 30px;
        color: var(--td-white);

        @media #{$xs} {
          font-size: 18px;
        }
  
        @media #{$sm} {
          font-size: 24px;
        }
      }

      &.swiper-button-disabled {
        border: 1px solid var(--edc-primary);
        background: rgba(7, 187, 168, 0.10);
      }
    }


    .testimonial-slider-box {
      width: 100%;
      max-width: 1064px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 50px;

      .testimonial-slider-box-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 50px;

        @media #{$md,$xs} {
          gap: 25px;
        }

        @media #{$xs} {
          gap: 5px;
        }
  
        @media #{$sm} {
          gap: 20px;
        }

        // @media #{$xs} {
        //   flex-direction: column;
        //   justify-content: start;
        //   align-items: start;
        // }

        // @media #{$sm} {
        //   flex-direction: row;
        //   justify-content: space-between;
        //   align-items: center;
        // }

        .edoc-testimonial-card {
          position: relative;
          margin-bottom: 40px;

          .edoc-testimonial-slider-card {
            padding: 50px;
            border-radius: 10px;
            background: #073352;
            box-shadow: 0px 20px 60px 0px rgba(46, 33, 61, 0.08);
            position: relative;
            z-index: 5;

            @media #{$md} {
              padding: 40px;
            }

            @media #{$xs} {
              padding: 10px;
            }
            @media #{$sm} {
              padding: 30px;
            }

            .content {
              position: relative;
              z-index: 5;

              .left {
                margin-right: 30px;

                @media #{$lg,$md,$xs} {
                  margin-right: 0px;
                }

                .img-box {
                  width: 100%;
                  height: 330px;
                  border-radius: 16px;

                  @media #{$md,$xs} {
                    height: 580px;
                  }

                  @media #{$xs} {
                    height: 24 0px;
                  }
                  @media #{$sm} {
                    height: 380px;
                  }

                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 16px;
                  }
                }
              }

              .right {
                h3 {
                  color: var(--td-white);
                  font-size: 40px;
                  font-weight: 500;
                  line-height: normal;
                  letter-spacing: -1.6px;
                  text-transform: capitalize;
                  margin-bottom: 16px;

                  @media #{$lg} {
                    font-size: 34px;
                  }

                  @media #{$md} {
                    font-size: 30px;
                  }

                  @media #{$xs} {
                    font-size: 26px;
                  }
                }

                p {
                  color: var(--td-white);
                  font-size: 16px;
                  font-style: normal;
                  font-weight: 300;
                  line-height: lh(26, 16);
                }
              }
            }

            .elements {
              .elem-1 {
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 4;
              }

              .elem-2 {
                position: absolute;
                bottom: 20px;
                right: 20px;
                z-index: 4;
              }
            }
          }

          &::after {
            content: '';
            height: 100px;
            width: 90%;
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
            border-radius: 10px;
            background: #245275;
            box-shadow: 0px 20px 60px 0px rgba(46, 33, 61, 0.08);
          }

          &::before {
            content: '';
            height: 100px;
            width: 80%;
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
            border-radius: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.07);
            box-shadow: 0px 20px 60px 0px rgba(46, 33, 61, 0.08);
          }

        }
      }
    }
  }
}