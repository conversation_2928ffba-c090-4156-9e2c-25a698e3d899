@use './mixins' as *;

%include-bg {
	@include background();
}

%td-transition-color {
	@include td-transition(color);
}

%td-transition-transform {
	@include td-transition(transform);
}

%td-transition-border-color {
	@include td-transition(border-color);
}

%td-transition-bg-color {
	@include td-transition(background-color);
}

%td-transition-fz {
	@include td-transition(font-size);
}

// Transition 
%td-transition-3 {
	@include td-transition();
}

%td-transition-5 {
	@include td-transition(opacity, 0.5s, ease-in-out, 0.2s);
}

// Font
%td-ff-inter {
	font-family: var(--td-ff-inter);
}

// Transform
%translateY1_2 {
	@include transform(translateY(-50%));
}

%translateX1_2 {
	@include transform(translateX(-50%));
}

%translate1_2 {
	@include transform(translate(-50%, -50%));
}

// Bg thumbnails
%bg-thumb {
	position: absolute;
	top: 0;
	inset-inline-start: 0;
	width: 100%;
	height: 100%;

}