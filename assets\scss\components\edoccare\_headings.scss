@use '../../utils' as *;

/*----------------------------------------*/
/*  Headings
/*----------------------------------------*/
.edoc-common-title {
  background: linear-gradient(90deg, #07bba840 0%, rgba(7, 187, 168, 0) 70%);
  border-left: 2px solid var(--edc-primary);
  display: inline-block;
  padding-left: 10px;
  margin-bottom: 10px;

  p {
    color: var(--edc-primary);
    font-size: 20px;
    font-weight: 400;
    line-height: normal;

    @media #{$md} {
      font-size: 18px;
      font-weight: 400;
    }

    @media #{$xs} {
      font-size: 16px;
      font-weight: 400;
    }
  }

  &-2 {
    margin-bottom: 16px;
  }
}

.edoc-website-title {
  color: var(--edc-heading);
  font-size: 80px;
  font-weight: 700;
  line-height: lh(90, 80);

  @media #{$xl} {
    font-size: 68px;
  }

  @media #{$lg} {
    font-size: 60px;
  }

  @media #{$md} {
    font-size: 52px;
  }

  @media #{$xs} {
    font-size: 32px;
  }

  @media #{$sm} {
    font-size: 42px;
  }
}

.edoc-website-subtitle {
  color: var(--edc-text-primary);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;

  @media #{$xs} {
    font-size: 16px;
  }

  @media #{$sm} {
    font-size: 18px;
  }
}

.edoc-common-section-title {
  width: 550px;
  display: flex;
  flex-direction: column;
  align-items: center;

  h2 {
    font-size: 48px;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 0;
    color: var(--edc-heading);
    text-align: center;

    @media #{$lg} {
      font-size: 36px;
    }

    @media #{$md} {
      font-size: 32px;
    }

    @media #{$xs} {
      font-size: 28px;
    }
  }

  &-2 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    @media #{$xs} {
      flex-direction: column;
      justify-content: start;
      align-items: start;
    }

    .right {
      @media #{$xs} {
        margin-top: 16px;
      }
    }
  }

  &-white {
    h2 {
      color: var(--td-white);
    }
  }

  &-sp {
    h2 {
      font-size: 30px;
      font-weight: 600;

      @media #{$lg} {
        font-size: 28px;
      }

      @media #{$md} {
        font-size: 25px;
      }

      @media #{$xs} {
        font-size: 20px;
      }
    }
  }

  &-3 {
    .left {
      h2 {
        width: 80%;
        text-align: left;
        margin-bottom: 40px;

        @media #{$xs} {
          margin-bottom: 20px;
        }
      }
    }
  }

  &-4 {
    .left {
      h2 {
        text-align: left;
        margin-bottom: 16px;
      }

      p {
        color: var(--edc-text-primary);
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
        margin-bottom: 40px;

        @media #{$xs} {
          margin-bottom: 20px;
        }
      }
    }
  }
}

.common-page-top {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 52px 0;

  @media #{$xl} {
    padding: 45px 0;
  }

  @media #{$lg} {
    padding: 40px 0;
  }

  @media #{$md,$xs} {
    padding: 35px 0;
  }

  @media #{$xs} {
    padding: 25px 0;
  }

  @media #{$sm} {
    padding: 30px 0;
  }

  .common-page-top-content {
    h2 {
      font-size: 48px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 16px;
      color: var(--edc-heading);

      @media #{$lg} {
        font-size: 36px;
      }

      @media #{$md} {
        font-size: 32px;
      }

      @media #{$xs} {
        font-size: 28px;
        margin-bottom: 10px;
      }
    }

    .breadcrumb-content {
      ul {
        list-style-type: none;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;

        li {
          .icon {
            display: flex;
            align-items: center;

            .double-arrow-right {
              font-size: 20px;
              color: var(--edc-text-primary);
            }
          }

          a {
            color: var(--edc-text-primary);
            font-size: 16px;
            font-weight: 500;
            line-height: normal;

            &.active {
              color: var(--edc-primary);
            }
          }
        }
      }
    }
  }
}

.sec-title-common {
  position: relative;
  margin-bottom: 20px;

  &::after {
    position: absolute;
    content: '';
    width: 40px;
    height: 3px;
    background: #07BBA8;
    bottom: 0;
    left: 0;
  }

  h5 {
    color: #093E63;
    font-size: 18px;
    font-weight: 600;
    line-height: normal;
    padding-bottom: 8px;
  }
}

.special-title {
  position: relative;
  margin-bottom: 16px;

  &::after {
    position: absolute;
    content: url('../images/edoccare/about-us/point.png');
    width: 46px;
    height: 16px;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
  }

  h3 {
    color: #093E63;
    font-size: 20px;
    font-weight: 600;
    line-height: normal;
    margin-left: 55px;
  }
}