@use '../../../utils' as *;

/*----------------------------------------*/
/*  doctor details
/*----------------------------------------*/
.edoc-our-doctor-details {
  position: relative;

  .edoc-our-doctor-details-content {
    .left {
      position: relative;
      z-index: 5;

      .doctor-details {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        padding-bottom: 30px;
        border-bottom: 1px solid rgba(9, 62, 99, 0.10);

        @media #{$xs} {
          flex-direction: column;
          justify-content: start;
          align-items: start;
          gap: 10px;
        }

        .left {
          h4 {
            color: var(--edc-heading);
            font-size: 30px;
            font-weight: 700;
            line-height: normal;
            margin-bottom: 10px;
          }

          p {
            color: var(--edc-primary);
            font-size: 18px;
            font-weight: 400;
            line-height: normal;
          }
        }

        .right {
          padding: 16px;
          border-radius: 16px;
          border: 1px solid #F3F3F3;
          margin-right: 110px;

          @media #{$lg,$md,$xs} {
            margin-right: 0px;
          }

          ul {
            list-style-type: none;

            li {
              display: flex;
              align-items: center;
              gap: 5px;
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }

              span {
                color: var(--edc-heading);
                font-size: 16px;
                font-weight: 500;
                line-height: normal;
              }

              color: var(--edc-text-primary);
              font-size: 16px;
              font-weight: 400;
              line-height: normal;
            }
          }
        }
      }

      .about-doctor {
        margin-top: 30px;

        p {
          color: var(--edc-text-primary);
          font-size: 16px;
          font-weight: 400;
          line-height: lh(26, 16);
        }
      }

      .doctor-education {
        margin-top: 30px;

        .education-table {
          width: 100%;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;

          .full-table {
            width: 100%;
            min-width: 600px;
            border-collapse: collapse;

            .table-light {
              --bs-table-color: #093E63;
              --bs-table-bg: #F2FAFF;
              --bs-table-border-color: #F2FAFF;
              --bs-table-striped-bg: #F2FAFF;
              --bs-table-striped-color: #093E63;
              --bs-table-active-bg: #F2FAFF;
              --bs-table-active-color: #093E63;
              --bs-table-hover-bg: #F2FAFF;
              --bs-table-hover-color: #093E63;
              color: var(--bs-table-color);
              border-color: var(--bs-table-border-color);
            }
          }

          .table>:not(caption)>*>* {
            padding: 10px 10px;
            color: #093E63;
            background-color: var(--bs-table-bg);
            border-bottom-width: none;
            box-shadow: none;
          }

          .table {
            tbody {
              tr {
                td {
                  color: #6B8BA1;
                }
              }
            }
          }
        }
      }

      .doctor-schedule {
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;

        .schedule-table {
          width: 100%;
          min-width: 700px;
          border-collapse: collapse;

          .schedule-table-full {
            .table {
              --bs-table-border-color: rgba(9, 62, 99, 0.10);
              --bs-table-accent-bg: transparent;
              --bs-table-striped-color: #093E63;
              border-color: rgba(9, 62, 99, 0.10);
              --bs-table-color: #6B8BA1;
            }

            .table>:not(caption)>*>* {
              padding: 10px 10px;
              color: #093E63;
            }

            .table {
              tbody {
                tr {
                  td {
                    color: #6B8BA1;
                  }
                }
              }
            }
          }
        }
      }
    }

    .right {
      margin-left: 50px;
      position: relative;
      z-index: 5;

      @media #{$xl,$lg,$md,$xs} {
        margin-left: 0px;
      }

      .doctor-box {
        position: relative;

        .pro {
          position: absolute;
          top: 0;
          left: -25%;

          @media #{$lg,$md,$xs} {
            left: -5%;
          }

          img {
            width: 178px;
            height: 178px;

            @media #{$lg,$md,$xs} {
              width: 100px;
              height: 100px;
            }
          }
        }

        .call {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: -25%;

          @media #{$xxl,$xl,$lg,$md,$xs} {
            right: 0%;
          }

          a {
            display: flex;
            align-items: center;
            gap: 10px;
            border-radius: 16px;
            background: #FFF;
            box-shadow: 0px 12px 56px 0px rgba(6, 28, 61, 0.12);
            padding: 16px;
            font-size: 16px;
            font-weight: 500;
            line-height: normal;
            text-decoration: none;
            gap: 15px;

            .icon {
              font-size: 30px;
            }

            .texts {
              .title {
                display: block;
                color: #093E63;
                font-size: 18px;
                font-weight: 700;
                line-height: normal;
                margin-bottom: 8px;
              }

              .subtitle {
                display: block;
                color: #093E63;
                font-size: 16px;
                font-weight: 500;
                line-height: normal;
              }
            }

          }
        }

        .doctor-img {
          width: 100%;
          height: 490px;

          @media #{$lg,$md,$xs} {
            height: 380px;
          }

          @media #{$md,$xs} {
            height: 100%;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .follow-box {
          background-color: var(--edc-heading);
          padding: 30px;
          border-radius: 0 0 16px 16px;
          display: flex;
          align-items: center;
          gap: 10px;

          @media #{$lg,$md,$xs} {
            flex-direction: column;
            gap: 20px;
          }

          h5 {
            color: var(--td-white);
            font-size: 20px;
            font-weight: 500;
            line-height: normal;
          }

          .follow-links {
            display: flex;
            align-items: center;
            gap: 10px;

            a {
              display: inline-flex;
              width: 40px;
              height: 40px;
              justify-content: center;
              align-items: center;
              gap: 10px;
              border-radius: 66px;
              border: 1px solid rgba(255, 255, 255, 0.10);
              background: rgba(255, 255, 255, 0.03);
              transition: all 0.3s ease-in-out;

              .social-icon {
                font-size: 20px;
                color: var(--td-white);
              }

              &:hover {
                border: 1px solid rgba(255, 255, 255, 0.20);
                background: rgba(255, 255, 255, 0.10);
              }
            }
          }
        }
      }

      .skills {
        margin-top: 30px;
        padding: 30px;
        border-radius: 16px;
        border: 1px solid rgba(34, 34, 34, 0.10);
      }
    }
  }

  .details-element {
    position: absolute;
    top: 40px;
    right: 40px;
    z-index: 1;

    @media #{$md,$xs} {
      display: none;
    }
  }
}