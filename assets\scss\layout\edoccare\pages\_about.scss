@use '../../../utils' as *;

/*----------------------------------------*/
/*  About
/*----------------------------------------*/
.edoc-about-us {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 187px;

  @media #{$lg} {
    padding-top: 100px;
  }

  @media #{$md,$xs} {
    padding-top: 64px;
  }

  @media #{$xs} {
    padding-top: 36px;
  }

  @media #{$xs} {
    padding-top: 50px;
  }

  .edoc-about-us-content {
    .left {
      position: relative;

      .main-img {
        height: 542px;
        position: relative;
        z-index: 5;

        @media #{$lg,$md,$xs} {
          height: 460px;
          transform: translateY(47px);
        }

        @media #{$md,$xs} {
          height: 380px;
          transform: translateY(0px);
          display: flex;
          justify-content: center;
        }

        @media #{$xs} {
          height: 290px;
        }

        @media #{$sm} {
          height: 380px;
        }

        img {
          height: 100%;
        }
      }

      .oval-element {
        width: 354px;
        height: 333px;
        flex-shrink: 0;
        background-color: var(--edc-primary);
        position: absolute;
        top: -100px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 50%;
        z-index: 4;

        @media #{$lg,$md,$xs} {
          top: -15px;
        }

        @media #{$xs} {
          width: 250px;
          height: 225px;
        }

        @media #{$sm} {
          width: 354px;
          height: 333px;
        }
      }
    }

    .right {
      padding-left: 145px;
      padding-bottom: 40px;

      @media #{$xl,$lg,$md,$xs} {
        padding-left: 45px;
      }

      @media #{$lg,$md} {
        padding-left: 35px;
      }

      @media #{$md,$xs} {
        padding-left: 0px;
      }

      .text {
        width: 85%;

        @media #{$xl,$lg,$md,$xs} {
          width: 100%;
        }

        h2 {
          color: var(--td-white);
          font-size: 48px;
          font-weight: 600;
          line-height: normal;
          margin-bottom: 16px;

          @media #{$xl} {
            font-size: 42px;
          }

          @media #{$lg} {
            font-size: 36px;
          }

          @media #{$md} {
            font-size: 32px;
          }

          @media #{$xs} {
            font-size: 28px;
          }
        }

        h6 {
          color: rgba(255, 255, 255, 0.60);
          font-size: 16px;
          font-weight: 400;
          line-height: 26px;
          margin-bottom: 30px;

          @media #{$xs} {
            font-size: 14px;
          }

          @media #{$sm} {
            font-size: 16px;
          }
        }
      }

      .learn-more-btn {
        margin-top: 40px;

        @media #{$xs} {
          margin-top: 10px;
        }
      }
    }
  }
}

.edoc-about-area {
  margin-top: 50px;

  .about-area-content {
    .left {
      .about-img {
        width: 100%;
        height: 100%;
        padding-right: 90px;

        @media #{$lg,$md,$xs} {
          padding-right: 0px;
        }

        img {
          height: 100%;
          width: 100%;
          object-fit: cover;
        }
      }

      .who-we-are {
        margin-top: 30px;

        p {
          color: var(--edc-text-primary);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: lh(26, 16);
          margin-left: 55px;
        }
      }
    }

    .right {
      margin-top: 50px;

      @media #{$xs} {
        margin-top: 0px;
      }

      .edoc-title {
        color: var(--edc-heading);
        font-size: 48px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 16px;

        @media #{$lg} {
          font-size: 36px;
        }

        @media #{$md} {
          font-size: 30px;
        }

        @media #{$xs} {
          font-size: 24px;
        }

        @media #{$sm} {
          font-size: 26px;
        }

      }

      .edoc-subtitle {
        color: var(--edc-text-primary);
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
      }

      .edc-progressbar-box {
        margin-top: 30px;
      }

      .action-btn {
        margin-top: 40px;
      }

      .company-stats {
        margin-top: 40px;

        .company-stats-box-col {
          .company-stats-box {
            padding: 40px;

            h2 {
              color: var(--td-white);
              text-align: center;
              font-size: 30px;
              font-style: normal;
              font-weight: 700;
              line-height: lh(36, 30);
              margin-bottom: 16px;
            }

            p {
              color: var(--td-white);
              text-align: center;
              font-size: 16px;
              font-weight: 400;
              line-height: normal;
            }
          }

          &:nth-child(1) {
            .company-stats-box {
              background-color: var(--edc-primary);
              border-radius: 16px 0px 0px 0px;

              @media #{$xs} {
                border-radius: 16px 16px 0px 0px;
              }

              @media #{$sm} {
                border-radius: 16px 0px 0px 0px;
              }
            }
          }

          &:nth-child(2) {
            .company-stats-box {
              background-color: var(--edc-heading);
              border-radius: 0px 16px 0px 0px;

              @media #{$xs} {
                border-radius: 0px 0px 0px 0px;
              }

              @media #{$sm} {
                border-radius: 0px 16px 0px 0px;
              }
            }
          }

          &:nth-child(3) {
            .company-stats-box {
              background-color: var(--edc-heading);
              border-radius: 0px 0px 0px 16px;

              @media #{$xs} {
                border-radius: 0px 0px 0px 0px;
              }

              @media #{$sm} {
                border-radius: 0px 0px 0px 16px;
              }
            }
          }

          &:nth-child(4) {
            .company-stats-box {
              background-color: var(--edc-primary);
              border-radius: 0px 0px 16px 0px;

              @media #{$xs} {
                border-radius: 0px 0px 16px 16px;
              }

              @media #{$sm} {
                border-radius: 0px 0px 16px 0px;
              }
            }
          }
        }
      }
    }
  }
}