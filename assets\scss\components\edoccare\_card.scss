@use '../../utils' as *;

/*----------------------------------------*/
/*  card
/*----------------------------------------*/
.edoc-choose-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
  border-radius: 16px;
  background: var(--td-white);
  border: 1px solid rgba(9, 62, 99, 0.10);
  height: 100%;

  .icon {
    width: 50px;
    height: 50px;
    flex-shrink: 0;
    margin-bottom: 20px;

    @media #{$xs} {
      width: 35px;
      height: 35px;
      margin-bottom: 16px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .text {
    h6 {
      color: var(--edc-heading);
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      margin-bottom: 8px;
    }

    p {
      color: var(--edc-text-primary);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: lh(20, 14);
    }
  }
}

.edoc-service-title-box {
  padding: 45px;
  border-radius: 32px;
  border: 1px solid var(--edc-primary);
  position: relative;
  overflow: hidden;
  height: 100%;

  @media #{$md,$xs} {
    padding: 30px;
  }

  @media #{$xs} {
    padding: 24px;
  }

  @media #{$sm} {
    padding: 30px;
  }

  h2 {
    color: var(--edc-heading);
    font-size: 48px;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 16px;

    @media #{$xl} {
      font-size: 42px;
    }

    @media #{$lg} {
      font-size: 36px;
    }

    @media #{$md} {
      font-size: 32px;
    }

    @media #{$xs} {
      font-size: 28px;
    }
  }

  p {
    color: var(--edc-text-primary);
    font-size: 16px;
    font-weight: 400;
    line-height: lh(22, 16);
  }

  .all-service-btn {
    margin-top: 40px;

    @media #{$xl,$lg,$md,$xs} {
      margin-top: 24px;
    }
  }

  .element {
    position: absolute;
    bottom: -50px;
    right: -57px;
  }
}

.edoc-service-card {
  border-radius: 32px;
  border: 1px solid var(--edc-primary);
  background: var(--td-white);
  position: relative;
  overflow: hidden;
  padding: 50px 16px;
  height: 100%;
  transition: all 0.3s ease-in-out;

  @media #{$xl,$lg,$md,$xs} {
    padding: 32px 22px;
  }

  @media #{$xs} {
    padding: 26px 14px;
  }

  @media #{$sm} {
    padding: 32px 22px;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .icon {
      margin-bottom: 16px;

      @media #{$xs} {
        margin-bottom: 12px;
      }

      @media #{$sm} {
        margin-bottom: 16px;
      }

      svg {
        width: 64px;
        height: 64px;

        @media #{$lg,$md,$xs} {
          width: 55px;
          height: 55px;
        }

        @media #{$xs} {
          width: 50px;
          height: 50px;
        }

        @media #{$sm} {
          width: 55px;
          height: 55px;
        }
      }
    }

    h3 {
      color: var(--edc-heading);
      font-size: 30px;
      font-weight: 500;
      line-height: normal;
      margin-bottom: 16px;

      @media #{$xl,$lg} {
        font-size: 26px;
      }

      @media #{$md,$xs} {
        font-size: 24px;
      }

      @media #{$xs} {
        margin-bottom: 12px;
      }

      @media #{$sm} {
        margin-bottom: 16px;
      }
    }

    p {
      color: var(--edc-text-primary);
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      line-height: lh(20, 14);
    }

    .learn-more-btn {
      margin-top: 40px;

      @media #{$lg,$md,$xs} {
        margin-top: 18px;
      }
    }
  }

  .element {
    position: absolute;
    bottom: 0px;
    right: 0px;
  }

  &:hover,
  &.active {
    background-color: var(--edc-primary);

    .content {
      .icon {
        svg {
          path {
            fill: var(--td-white);
          }
        }
      }

      h3 {
        color: var(--td-white);
      }

      p {
        color: var(--td-white);
      }

      .learn-more-btn {
        a {
          color: var(--td-white);
        }
      }
    }

    .element {
      svg {
        g {
          circle {
            fill: var(--td-white);
            stroke: var(--td-white);
          }
        }
      }
    }
  }

  &.active {
    &:hover {
      background-color: var(--td-white);

      .content {
        .icon {
          svg {
            path {
              fill: var(--edc-heading);
            }
          }
        }

        h3 {
          color: var(--edc-heading);
        }

        p {
          color: var(--edc-text-primary);
        }

        .learn-more-btn {
          a {
            color: var(--edc-heading);
          }
        }
      }
    }
  }
}

.edoc-doctor-card {
  display: block;
  border-radius: 16px;
  background: var(--edc-primary);
  padding-top: 30px;
  position: relative;
  overflow: hidden; // Add this to prevent content from showing outside the card

  .doctor-image {
    display: flex;
    justify-content: center;
    height: 310px;

    @media #{$xs} {
      height: 220px;
    }

    img {
      height: 100%;
    }
  }

  .shape {
    position: absolute;
    top: 0;
    right: 0;
  }

  .content {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    transform: translateY(100%);
    opacity: 0;
    transition: all 0.5s ease-in-out;

    .box {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: 20px;
      padding-top: 100px;
      width: 100%;
      position: relative;
      z-index: 5;
      padding-left: 10px;
      padding-right: 10px;

      .text {
        position: relative;
        z-index: 5;

        h4 {
          color: var(--td-white);
          font-size: 26px;
          font-weight: 600;
          line-height: normal;
          letter-spacing: 0.2px;

          @media #{$lg} {
            font-size: 24px;
          }

          @media #{$md} {
            font-size: 22px;
          }

          @media #{$xs} {
            font-size: 20px;
          }
        }

        p {
          color: var(--td-white);
          text-align: center;
          font-size: 16px;
          font-weight: 400;
          line-height: normal;
          letter-spacing: 0.2px;
          margin-bottom: 10px;
        }
      }

      .view-profile {
        position: relative;
        z-index: 5;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, rgba(7, 187, 168, 0.00) 0%, rgba(9, 62, 99, 0.77) 73.56%, #093E63 100%);
        z-index: 0;
        border-radius: 16px;
      }
    }
  }

  &:hover {
    .content {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

.edc-our-blog-card {
  padding: 16px;
  border-radius: 24px;
  border: 1px solid rgba(107, 139, 161, 0.10);

  .img-box {
    width: 100%;
    height: 222px;
    display: block;
    border-radius: 8px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
  }

  .content {
    .post-date {
      display: flex;
      align-items: center;
      gap: 50px;
      margin-bottom: 10px;

      .post,
      .date {
        display: flex;
        align-items: center;
        gap: 7px;

        .icon {
          display: flex;

          .post-icon {
            color: var(--edc-text-primary);
            font-size: 16px;
          }
        }

        p {
          color: var(--edc-text-primary);
          font-size: 16px;
          font-weight: 400;
          line-height: normal;
        }
      }
    }

    .title {
      span {
        display: block;
        color: var(--edc-heading);
        font-size: 20px;
        font-weight: 600;
        line-height: lh(30, 20);
        margin-bottom: 10px;
        transition: all 0.3s ease-in-out;

        &:hover {
          color: var(--edc-primary);
        }
      }
    }

    p {
      color: var(--edc-text-primary);
      font-size: 16px;
      font-weight: 400;
      line-height: lh(26, 16);
    }

    .read-more-btn {
      margin-top: 22px;
    }
  }
}

.edoc-service-points-card {
  position: relative;
  padding-left: 24px;
  height: 100%;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: rgba(9, 62, 99, 0.06);
    border-radius: 0 50px 50px 0;
  }

  .icon {
    width: 50px;
    height: 50px;
    margin-bottom: 16px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .text {
    h6 {
      color: var(--edc-heading);
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 8px;
    }

    p {
      color: var(--edc-text-primary);
      font-size: 14px;
      font-weight: 400;
      line-height: lh(22, 14);
    }
  }
}