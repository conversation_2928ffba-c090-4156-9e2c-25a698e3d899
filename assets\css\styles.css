/*-----------------------------------------------------------------------------------

  Project Name:  Multipurpose Theme
  Author: Tdevs
  Support: 
  Description:  We are dedicated to being your website live. 
  Version: 1.0

-----------------------------------------------------------------------------------

/*----------------------------------------*/
/*   1.1 Globals
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap");
.mt-1 {
  margin-top: 1px;
}

.mb-1 {
  margin-bottom: 1px;
}

.ml-1 {
  margin-left: 1px;
}

.mr-1 {
  margin-right: 1px;
}

.mt-2 {
  margin-top: 2px;
}

.mb-2 {
  margin-bottom: 2px;
}

.ml-2 {
  margin-left: 2px;
}

.mr-2 {
  margin-right: 2px;
}

.mt-3 {
  margin-top: 3px;
}

.mb-3 {
  margin-bottom: 3px;
}

.ml-3 {
  margin-left: 3px;
}

.mr-3 {
  margin-right: 3px;
}

.mt-4 {
  margin-top: 4px;
}

.mb-4 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-left: 4px;
}

.mr-4 {
  margin-right: 4px;
}

.mt-5 {
  margin-top: 5px;
}

.mb-5 {
  margin-bottom: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.mr-5 {
  margin-right: 5px;
}

.mt-6 {
  margin-top: 6px;
}

.mb-6 {
  margin-bottom: 6px;
}

.ml-6 {
  margin-left: 6px;
}

.mr-6 {
  margin-right: 6px;
}

.mt-7 {
  margin-top: 7px;
}

.mb-7 {
  margin-bottom: 7px;
}

.ml-7 {
  margin-left: 7px;
}

.mr-7 {
  margin-right: 7px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.ml-8 {
  margin-left: 8px;
}

.mr-8 {
  margin-right: 8px;
}

.mt-9 {
  margin-top: 9px;
}

.mb-9 {
  margin-bottom: 9px;
}

.ml-9 {
  margin-left: 9px;
}

.mr-9 {
  margin-right: 9px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mt-11 {
  margin-top: 11px;
}

.mb-11 {
  margin-bottom: 11px;
}

.ml-11 {
  margin-left: 11px;
}

.mr-11 {
  margin-right: 11px;
}

.mt-12 {
  margin-top: 12px;
}

.mb-12 {
  margin-bottom: 12px;
}

.ml-12 {
  margin-left: 12px;
}

.mr-12 {
  margin-right: 12px;
}

.mt-13 {
  margin-top: 13px;
}

.mb-13 {
  margin-bottom: 13px;
}

.ml-13 {
  margin-left: 13px;
}

.mr-13 {
  margin-right: 13px;
}

.mt-14 {
  margin-top: 14px;
}

.mb-14 {
  margin-bottom: 14px;
}

.ml-14 {
  margin-left: 14px;
}

.mr-14 {
  margin-right: 14px;
}

.mt-15 {
  margin-top: 15px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-15 {
  margin-left: 15px;
}

.mr-15 {
  margin-right: 15px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-16 {
  margin-left: 16px;
}

.mr-16 {
  margin-right: 16px;
}

.mt-17 {
  margin-top: 17px;
}

.mb-17 {
  margin-bottom: 17px;
}

.ml-17 {
  margin-left: 17px;
}

.mr-17 {
  margin-right: 17px;
}

.mt-18 {
  margin-top: 18px;
}

.mb-18 {
  margin-bottom: 18px;
}

.ml-18 {
  margin-left: 18px;
}

.mr-18 {
  margin-right: 18px;
}

.mt-19 {
  margin-top: 19px;
}

.mb-19 {
  margin-bottom: 19px;
}

.ml-19 {
  margin-left: 19px;
}

.mr-19 {
  margin-right: 19px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-20 {
  margin-left: 20px;
}

.mr-20 {
  margin-right: 20px;
}

.mt-21 {
  margin-top: 21px;
}

.mb-21 {
  margin-bottom: 21px;
}

.ml-21 {
  margin-left: 21px;
}

.mr-21 {
  margin-right: 21px;
}

.mt-22 {
  margin-top: 22px;
}

.mb-22 {
  margin-bottom: 22px;
}

.ml-22 {
  margin-left: 22px;
}

.mr-22 {
  margin-right: 22px;
}

.mt-23 {
  margin-top: 23px;
}

.mb-23 {
  margin-bottom: 23px;
}

.ml-23 {
  margin-left: 23px;
}

.mr-23 {
  margin-right: 23px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.ml-24 {
  margin-left: 24px;
}

.mr-24 {
  margin-right: 24px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-25 {
  margin-bottom: 25px;
}

.ml-25 {
  margin-left: 25px;
}

.mr-25 {
  margin-right: 25px;
}

.mt-26 {
  margin-top: 26px;
}

.mb-26 {
  margin-bottom: 26px;
}

.ml-26 {
  margin-left: 26px;
}

.mr-26 {
  margin-right: 26px;
}

.mt-27 {
  margin-top: 27px;
}

.mb-27 {
  margin-bottom: 27px;
}

.ml-27 {
  margin-left: 27px;
}

.mr-27 {
  margin-right: 27px;
}

.mt-28 {
  margin-top: 28px;
}

.mb-28 {
  margin-bottom: 28px;
}

.ml-28 {
  margin-left: 28px;
}

.mr-28 {
  margin-right: 28px;
}

.mt-29 {
  margin-top: 29px;
}

.mb-29 {
  margin-bottom: 29px;
}

.ml-29 {
  margin-left: 29px;
}

.mr-29 {
  margin-right: 29px;
}

.mt-30 {
  margin-top: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-30 {
  margin-left: 30px;
}

.mr-30 {
  margin-right: 30px;
}

.mt-31 {
  margin-top: 31px;
}

.mb-31 {
  margin-bottom: 31px;
}

.ml-31 {
  margin-left: 31px;
}

.mr-31 {
  margin-right: 31px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-32 {
  margin-bottom: 32px;
}

.ml-32 {
  margin-left: 32px;
}

.mr-32 {
  margin-right: 32px;
}

.mt-33 {
  margin-top: 33px;
}

.mb-33 {
  margin-bottom: 33px;
}

.ml-33 {
  margin-left: 33px;
}

.mr-33 {
  margin-right: 33px;
}

.mt-34 {
  margin-top: 34px;
}

.mb-34 {
  margin-bottom: 34px;
}

.ml-34 {
  margin-left: 34px;
}

.mr-34 {
  margin-right: 34px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-35 {
  margin-bottom: 35px;
}

.ml-35 {
  margin-left: 35px;
}

.mr-35 {
  margin-right: 35px;
}

.mt-36 {
  margin-top: 36px;
}

.mb-36 {
  margin-bottom: 36px;
}

.ml-36 {
  margin-left: 36px;
}

.mr-36 {
  margin-right: 36px;
}

.mt-37 {
  margin-top: 37px;
}

.mb-37 {
  margin-bottom: 37px;
}

.ml-37 {
  margin-left: 37px;
}

.mr-37 {
  margin-right: 37px;
}

.mt-38 {
  margin-top: 38px;
}

.mb-38 {
  margin-bottom: 38px;
}

.ml-38 {
  margin-left: 38px;
}

.mr-38 {
  margin-right: 38px;
}

.mt-39 {
  margin-top: 39px;
}

.mb-39 {
  margin-bottom: 39px;
}

.ml-39 {
  margin-left: 39px;
}

.mr-39 {
  margin-right: 39px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.ml-40 {
  margin-left: 40px;
}

.mr-40 {
  margin-right: 40px;
}

.mt-41 {
  margin-top: 41px;
}

.mb-41 {
  margin-bottom: 41px;
}

.ml-41 {
  margin-left: 41px;
}

.mr-41 {
  margin-right: 41px;
}

.mt-42 {
  margin-top: 42px;
}

.mb-42 {
  margin-bottom: 42px;
}

.ml-42 {
  margin-left: 42px;
}

.mr-42 {
  margin-right: 42px;
}

.mt-43 {
  margin-top: 43px;
}

.mb-43 {
  margin-bottom: 43px;
}

.ml-43 {
  margin-left: 43px;
}

.mr-43 {
  margin-right: 43px;
}

.mt-44 {
  margin-top: 44px;
}

.mb-44 {
  margin-bottom: 44px;
}

.ml-44 {
  margin-left: 44px;
}

.mr-44 {
  margin-right: 44px;
}

.mt-45 {
  margin-top: 45px;
}

.mb-45 {
  margin-bottom: 45px;
}

.ml-45 {
  margin-left: 45px;
}

.mr-45 {
  margin-right: 45px;
}

.mt-46 {
  margin-top: 46px;
}

.mb-46 {
  margin-bottom: 46px;
}

.ml-46 {
  margin-left: 46px;
}

.mr-46 {
  margin-right: 46px;
}

.mt-47 {
  margin-top: 47px;
}

.mb-47 {
  margin-bottom: 47px;
}

.ml-47 {
  margin-left: 47px;
}

.mr-47 {
  margin-right: 47px;
}

.mt-48 {
  margin-top: 48px;
}

.mb-48 {
  margin-bottom: 48px;
}

.ml-48 {
  margin-left: 48px;
}

.mr-48 {
  margin-right: 48px;
}

.mt-49 {
  margin-top: 49px;
}

.mb-49 {
  margin-bottom: 49px;
}

.ml-49 {
  margin-left: 49px;
}

.mr-49 {
  margin-right: 49px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-50 {
  margin-bottom: 50px;
}

.ml-50 {
  margin-left: 50px;
}

.mr-50 {
  margin-right: 50px;
}

.mt-51 {
  margin-top: 51px;
}

.mb-51 {
  margin-bottom: 51px;
}

.ml-51 {
  margin-left: 51px;
}

.mr-51 {
  margin-right: 51px;
}

.mt-52 {
  margin-top: 52px;
}

.mb-52 {
  margin-bottom: 52px;
}

.ml-52 {
  margin-left: 52px;
}

.mr-52 {
  margin-right: 52px;
}

.mt-53 {
  margin-top: 53px;
}

.mb-53 {
  margin-bottom: 53px;
}

.ml-53 {
  margin-left: 53px;
}

.mr-53 {
  margin-right: 53px;
}

.mt-54 {
  margin-top: 54px;
}

.mb-54 {
  margin-bottom: 54px;
}

.ml-54 {
  margin-left: 54px;
}

.mr-54 {
  margin-right: 54px;
}

.mt-55 {
  margin-top: 55px;
}

.mb-55 {
  margin-bottom: 55px;
}

.ml-55 {
  margin-left: 55px;
}

.mr-55 {
  margin-right: 55px;
}

.mt-56 {
  margin-top: 56px;
}

.mb-56 {
  margin-bottom: 56px;
}

.ml-56 {
  margin-left: 56px;
}

.mr-56 {
  margin-right: 56px;
}

.mt-57 {
  margin-top: 57px;
}

.mb-57 {
  margin-bottom: 57px;
}

.ml-57 {
  margin-left: 57px;
}

.mr-57 {
  margin-right: 57px;
}

.mt-58 {
  margin-top: 58px;
}

.mb-58 {
  margin-bottom: 58px;
}

.ml-58 {
  margin-left: 58px;
}

.mr-58 {
  margin-right: 58px;
}

.mt-59 {
  margin-top: 59px;
}

.mb-59 {
  margin-bottom: 59px;
}

.ml-59 {
  margin-left: 59px;
}

.mr-59 {
  margin-right: 59px;
}

.mt-60 {
  margin-top: 60px;
}

.mb-60 {
  margin-bottom: 60px;
}

.ml-60 {
  margin-left: 60px;
}

.mr-60 {
  margin-right: 60px;
}

.mt-61 {
  margin-top: 61px;
}

.mb-61 {
  margin-bottom: 61px;
}

.ml-61 {
  margin-left: 61px;
}

.mr-61 {
  margin-right: 61px;
}

.mt-62 {
  margin-top: 62px;
}

.mb-62 {
  margin-bottom: 62px;
}

.ml-62 {
  margin-left: 62px;
}

.mr-62 {
  margin-right: 62px;
}

.mt-63 {
  margin-top: 63px;
}

.mb-63 {
  margin-bottom: 63px;
}

.ml-63 {
  margin-left: 63px;
}

.mr-63 {
  margin-right: 63px;
}

.mt-64 {
  margin-top: 64px;
}

.mb-64 {
  margin-bottom: 64px;
}

.ml-64 {
  margin-left: 64px;
}

.mr-64 {
  margin-right: 64px;
}

.mt-65 {
  margin-top: 65px;
}

.mb-65 {
  margin-bottom: 65px;
}

.ml-65 {
  margin-left: 65px;
}

.mr-65 {
  margin-right: 65px;
}

.mt-66 {
  margin-top: 66px;
}

.mb-66 {
  margin-bottom: 66px;
}

.ml-66 {
  margin-left: 66px;
}

.mr-66 {
  margin-right: 66px;
}

.mt-67 {
  margin-top: 67px;
}

.mb-67 {
  margin-bottom: 67px;
}

.ml-67 {
  margin-left: 67px;
}

.mr-67 {
  margin-right: 67px;
}

.mt-68 {
  margin-top: 68px;
}

.mb-68 {
  margin-bottom: 68px;
}

.ml-68 {
  margin-left: 68px;
}

.mr-68 {
  margin-right: 68px;
}

.mt-69 {
  margin-top: 69px;
}

.mb-69 {
  margin-bottom: 69px;
}

.ml-69 {
  margin-left: 69px;
}

.mr-69 {
  margin-right: 69px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-70 {
  margin-bottom: 70px;
}

.ml-70 {
  margin-left: 70px;
}

.mr-70 {
  margin-right: 70px;
}

.mt-71 {
  margin-top: 71px;
}

.mb-71 {
  margin-bottom: 71px;
}

.ml-71 {
  margin-left: 71px;
}

.mr-71 {
  margin-right: 71px;
}

.mt-72 {
  margin-top: 72px;
}

.mb-72 {
  margin-bottom: 72px;
}

.ml-72 {
  margin-left: 72px;
}

.mr-72 {
  margin-right: 72px;
}

.mt-73 {
  margin-top: 73px;
}

.mb-73 {
  margin-bottom: 73px;
}

.ml-73 {
  margin-left: 73px;
}

.mr-73 {
  margin-right: 73px;
}

.mt-74 {
  margin-top: 74px;
}

.mb-74 {
  margin-bottom: 74px;
}

.ml-74 {
  margin-left: 74px;
}

.mr-74 {
  margin-right: 74px;
}

.mt-75 {
  margin-top: 75px;
}

.mb-75 {
  margin-bottom: 75px;
}

.ml-75 {
  margin-left: 75px;
}

.mr-75 {
  margin-right: 75px;
}

.mt-76 {
  margin-top: 76px;
}

.mb-76 {
  margin-bottom: 76px;
}

.ml-76 {
  margin-left: 76px;
}

.mr-76 {
  margin-right: 76px;
}

.mt-77 {
  margin-top: 77px;
}

.mb-77 {
  margin-bottom: 77px;
}

.ml-77 {
  margin-left: 77px;
}

.mr-77 {
  margin-right: 77px;
}

.mt-78 {
  margin-top: 78px;
}

.mb-78 {
  margin-bottom: 78px;
}

.ml-78 {
  margin-left: 78px;
}

.mr-78 {
  margin-right: 78px;
}

.mt-79 {
  margin-top: 79px;
}

.mb-79 {
  margin-bottom: 79px;
}

.ml-79 {
  margin-left: 79px;
}

.mr-79 {
  margin-right: 79px;
}

.mt-80 {
  margin-top: 80px;
}

.mb-80 {
  margin-bottom: 80px;
}

.ml-80 {
  margin-left: 80px;
}

.mr-80 {
  margin-right: 80px;
}

.mt-81 {
  margin-top: 81px;
}

.mb-81 {
  margin-bottom: 81px;
}

.ml-81 {
  margin-left: 81px;
}

.mr-81 {
  margin-right: 81px;
}

.mt-82 {
  margin-top: 82px;
}

.mb-82 {
  margin-bottom: 82px;
}

.ml-82 {
  margin-left: 82px;
}

.mr-82 {
  margin-right: 82px;
}

.mt-83 {
  margin-top: 83px;
}

.mb-83 {
  margin-bottom: 83px;
}

.ml-83 {
  margin-left: 83px;
}

.mr-83 {
  margin-right: 83px;
}

.mt-84 {
  margin-top: 84px;
}

.mb-84 {
  margin-bottom: 84px;
}

.ml-84 {
  margin-left: 84px;
}

.mr-84 {
  margin-right: 84px;
}

.mt-85 {
  margin-top: 85px;
}

.mb-85 {
  margin-bottom: 85px;
}

.ml-85 {
  margin-left: 85px;
}

.mr-85 {
  margin-right: 85px;
}

.mt-86 {
  margin-top: 86px;
}

.mb-86 {
  margin-bottom: 86px;
}

.ml-86 {
  margin-left: 86px;
}

.mr-86 {
  margin-right: 86px;
}

.mt-87 {
  margin-top: 87px;
}

.mb-87 {
  margin-bottom: 87px;
}

.ml-87 {
  margin-left: 87px;
}

.mr-87 {
  margin-right: 87px;
}

.mt-88 {
  margin-top: 88px;
}

.mb-88 {
  margin-bottom: 88px;
}

.ml-88 {
  margin-left: 88px;
}

.mr-88 {
  margin-right: 88px;
}

.mt-89 {
  margin-top: 89px;
}

.mb-89 {
  margin-bottom: 89px;
}

.ml-89 {
  margin-left: 89px;
}

.mr-89 {
  margin-right: 89px;
}

.mt-90 {
  margin-top: 90px;
}

.mb-90 {
  margin-bottom: 90px;
}

.ml-90 {
  margin-left: 90px;
}

.mr-90 {
  margin-right: 90px;
}

.mt-91 {
  margin-top: 91px;
}

.mb-91 {
  margin-bottom: 91px;
}

.ml-91 {
  margin-left: 91px;
}

.mr-91 {
  margin-right: 91px;
}

.mt-92 {
  margin-top: 92px;
}

.mb-92 {
  margin-bottom: 92px;
}

.ml-92 {
  margin-left: 92px;
}

.mr-92 {
  margin-right: 92px;
}

.mt-93 {
  margin-top: 93px;
}

.mb-93 {
  margin-bottom: 93px;
}

.ml-93 {
  margin-left: 93px;
}

.mr-93 {
  margin-right: 93px;
}

.mt-94 {
  margin-top: 94px;
}

.mb-94 {
  margin-bottom: 94px;
}

.ml-94 {
  margin-left: 94px;
}

.mr-94 {
  margin-right: 94px;
}

.mt-95 {
  margin-top: 95px;
}

.mb-95 {
  margin-bottom: 95px;
}

.ml-95 {
  margin-left: 95px;
}

.mr-95 {
  margin-right: 95px;
}

.mt-96 {
  margin-top: 96px;
}

.mb-96 {
  margin-bottom: 96px;
}

.ml-96 {
  margin-left: 96px;
}

.mr-96 {
  margin-right: 96px;
}

.mt-97 {
  margin-top: 97px;
}

.mb-97 {
  margin-bottom: 97px;
}

.ml-97 {
  margin-left: 97px;
}

.mr-97 {
  margin-right: 97px;
}

.mt-98 {
  margin-top: 98px;
}

.mb-98 {
  margin-bottom: 98px;
}

.ml-98 {
  margin-left: 98px;
}

.mr-98 {
  margin-right: 98px;
}

.mt-99 {
  margin-top: 99px;
}

.mb-99 {
  margin-bottom: 99px;
}

.ml-99 {
  margin-left: 99px;
}

.mr-99 {
  margin-right: 99px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-100 {
  margin-bottom: 100px;
}

.ml-100 {
  margin-left: 100px;
}

.mr-100 {
  margin-right: 100px;
}

.mt-101 {
  margin-top: 101px;
}

.mb-101 {
  margin-bottom: 101px;
}

.ml-101 {
  margin-left: 101px;
}

.mr-101 {
  margin-right: 101px;
}

.mt-102 {
  margin-top: 102px;
}

.mb-102 {
  margin-bottom: 102px;
}

.ml-102 {
  margin-left: 102px;
}

.mr-102 {
  margin-right: 102px;
}

.mt-103 {
  margin-top: 103px;
}

.mb-103 {
  margin-bottom: 103px;
}

.ml-103 {
  margin-left: 103px;
}

.mr-103 {
  margin-right: 103px;
}

.mt-104 {
  margin-top: 104px;
}

.mb-104 {
  margin-bottom: 104px;
}

.ml-104 {
  margin-left: 104px;
}

.mr-104 {
  margin-right: 104px;
}

.mt-105 {
  margin-top: 105px;
}

.mb-105 {
  margin-bottom: 105px;
}

.ml-105 {
  margin-left: 105px;
}

.mr-105 {
  margin-right: 105px;
}

.mt-106 {
  margin-top: 106px;
}

.mb-106 {
  margin-bottom: 106px;
}

.ml-106 {
  margin-left: 106px;
}

.mr-106 {
  margin-right: 106px;
}

.mt-107 {
  margin-top: 107px;
}

.mb-107 {
  margin-bottom: 107px;
}

.ml-107 {
  margin-left: 107px;
}

.mr-107 {
  margin-right: 107px;
}

.mt-108 {
  margin-top: 108px;
}

.mb-108 {
  margin-bottom: 108px;
}

.ml-108 {
  margin-left: 108px;
}

.mr-108 {
  margin-right: 108px;
}

.mt-109 {
  margin-top: 109px;
}

.mb-109 {
  margin-bottom: 109px;
}

.ml-109 {
  margin-left: 109px;
}

.mr-109 {
  margin-right: 109px;
}

.mt-110 {
  margin-top: 110px;
}

.mb-110 {
  margin-bottom: 110px;
}

.ml-110 {
  margin-left: 110px;
}

.mr-110 {
  margin-right: 110px;
}

.mt-111 {
  margin-top: 111px;
}

.mb-111 {
  margin-bottom: 111px;
}

.ml-111 {
  margin-left: 111px;
}

.mr-111 {
  margin-right: 111px;
}

.mt-112 {
  margin-top: 112px;
}

.mb-112 {
  margin-bottom: 112px;
}

.ml-112 {
  margin-left: 112px;
}

.mr-112 {
  margin-right: 112px;
}

.mt-113 {
  margin-top: 113px;
}

.mb-113 {
  margin-bottom: 113px;
}

.ml-113 {
  margin-left: 113px;
}

.mr-113 {
  margin-right: 113px;
}

.mt-114 {
  margin-top: 114px;
}

.mb-114 {
  margin-bottom: 114px;
}

.ml-114 {
  margin-left: 114px;
}

.mr-114 {
  margin-right: 114px;
}

.mt-115 {
  margin-top: 115px;
}

.mb-115 {
  margin-bottom: 115px;
}

.ml-115 {
  margin-left: 115px;
}

.mr-115 {
  margin-right: 115px;
}

.mt-116 {
  margin-top: 116px;
}

.mb-116 {
  margin-bottom: 116px;
}

.ml-116 {
  margin-left: 116px;
}

.mr-116 {
  margin-right: 116px;
}

.mt-117 {
  margin-top: 117px;
}

.mb-117 {
  margin-bottom: 117px;
}

.ml-117 {
  margin-left: 117px;
}

.mr-117 {
  margin-right: 117px;
}

.mt-118 {
  margin-top: 118px;
}

.mb-118 {
  margin-bottom: 118px;
}

.ml-118 {
  margin-left: 118px;
}

.mr-118 {
  margin-right: 118px;
}

.mt-119 {
  margin-top: 119px;
}

.mb-119 {
  margin-bottom: 119px;
}

.ml-119 {
  margin-left: 119px;
}

.mr-119 {
  margin-right: 119px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-120 {
  margin-bottom: 120px;
}

.ml-120 {
  margin-left: 120px;
}

.mr-120 {
  margin-right: 120px;
}

.mt-121 {
  margin-top: 121px;
}

.mb-121 {
  margin-bottom: 121px;
}

.ml-121 {
  margin-left: 121px;
}

.mr-121 {
  margin-right: 121px;
}

.mt-122 {
  margin-top: 122px;
}

.mb-122 {
  margin-bottom: 122px;
}

.ml-122 {
  margin-left: 122px;
}

.mr-122 {
  margin-right: 122px;
}

.mt-123 {
  margin-top: 123px;
}

.mb-123 {
  margin-bottom: 123px;
}

.ml-123 {
  margin-left: 123px;
}

.mr-123 {
  margin-right: 123px;
}

.mt-124 {
  margin-top: 124px;
}

.mb-124 {
  margin-bottom: 124px;
}

.ml-124 {
  margin-left: 124px;
}

.mr-124 {
  margin-right: 124px;
}

.mt-125 {
  margin-top: 125px;
}

.mb-125 {
  margin-bottom: 125px;
}

.ml-125 {
  margin-left: 125px;
}

.mr-125 {
  margin-right: 125px;
}

.mt-126 {
  margin-top: 126px;
}

.mb-126 {
  margin-bottom: 126px;
}

.ml-126 {
  margin-left: 126px;
}

.mr-126 {
  margin-right: 126px;
}

.mt-127 {
  margin-top: 127px;
}

.mb-127 {
  margin-bottom: 127px;
}

.ml-127 {
  margin-left: 127px;
}

.mr-127 {
  margin-right: 127px;
}

.mt-128 {
  margin-top: 128px;
}

.mb-128 {
  margin-bottom: 128px;
}

.ml-128 {
  margin-left: 128px;
}

.mr-128 {
  margin-right: 128px;
}

.mt-129 {
  margin-top: 129px;
}

.mb-129 {
  margin-bottom: 129px;
}

.ml-129 {
  margin-left: 129px;
}

.mr-129 {
  margin-right: 129px;
}

.mt-130 {
  margin-top: 130px;
}

.mb-130 {
  margin-bottom: 130px;
}

.ml-130 {
  margin-left: 130px;
}

.mr-130 {
  margin-right: 130px;
}

.mt-131 {
  margin-top: 131px;
}

.mb-131 {
  margin-bottom: 131px;
}

.ml-131 {
  margin-left: 131px;
}

.mr-131 {
  margin-right: 131px;
}

.mt-132 {
  margin-top: 132px;
}

.mb-132 {
  margin-bottom: 132px;
}

.ml-132 {
  margin-left: 132px;
}

.mr-132 {
  margin-right: 132px;
}

.mt-133 {
  margin-top: 133px;
}

.mb-133 {
  margin-bottom: 133px;
}

.ml-133 {
  margin-left: 133px;
}

.mr-133 {
  margin-right: 133px;
}

.mt-134 {
  margin-top: 134px;
}

.mb-134 {
  margin-bottom: 134px;
}

.ml-134 {
  margin-left: 134px;
}

.mr-134 {
  margin-right: 134px;
}

.mt-135 {
  margin-top: 135px;
}

.mb-135 {
  margin-bottom: 135px;
}

.ml-135 {
  margin-left: 135px;
}

.mr-135 {
  margin-right: 135px;
}

.mt-136 {
  margin-top: 136px;
}

.mb-136 {
  margin-bottom: 136px;
}

.ml-136 {
  margin-left: 136px;
}

.mr-136 {
  margin-right: 136px;
}

.mt-137 {
  margin-top: 137px;
}

.mb-137 {
  margin-bottom: 137px;
}

.ml-137 {
  margin-left: 137px;
}

.mr-137 {
  margin-right: 137px;
}

.mt-138 {
  margin-top: 138px;
}

.mb-138 {
  margin-bottom: 138px;
}

.ml-138 {
  margin-left: 138px;
}

.mr-138 {
  margin-right: 138px;
}

.mt-139 {
  margin-top: 139px;
}

.mb-139 {
  margin-bottom: 139px;
}

.ml-139 {
  margin-left: 139px;
}

.mr-139 {
  margin-right: 139px;
}

.mt-140 {
  margin-top: 140px;
}

.mb-140 {
  margin-bottom: 140px;
}

.ml-140 {
  margin-left: 140px;
}

.mr-140 {
  margin-right: 140px;
}

.mt-141 {
  margin-top: 141px;
}

.mb-141 {
  margin-bottom: 141px;
}

.ml-141 {
  margin-left: 141px;
}

.mr-141 {
  margin-right: 141px;
}

.mt-142 {
  margin-top: 142px;
}

.mb-142 {
  margin-bottom: 142px;
}

.ml-142 {
  margin-left: 142px;
}

.mr-142 {
  margin-right: 142px;
}

.mt-143 {
  margin-top: 143px;
}

.mb-143 {
  margin-bottom: 143px;
}

.ml-143 {
  margin-left: 143px;
}

.mr-143 {
  margin-right: 143px;
}

.mt-144 {
  margin-top: 144px;
}

.mb-144 {
  margin-bottom: 144px;
}

.ml-144 {
  margin-left: 144px;
}

.mr-144 {
  margin-right: 144px;
}

.mt-145 {
  margin-top: 145px;
}

.mb-145 {
  margin-bottom: 145px;
}

.ml-145 {
  margin-left: 145px;
}

.mr-145 {
  margin-right: 145px;
}

.mt-146 {
  margin-top: 146px;
}

.mb-146 {
  margin-bottom: 146px;
}

.ml-146 {
  margin-left: 146px;
}

.mr-146 {
  margin-right: 146px;
}

.mt-147 {
  margin-top: 147px;
}

.mb-147 {
  margin-bottom: 147px;
}

.ml-147 {
  margin-left: 147px;
}

.mr-147 {
  margin-right: 147px;
}

.mt-148 {
  margin-top: 148px;
}

.mb-148 {
  margin-bottom: 148px;
}

.ml-148 {
  margin-left: 148px;
}

.mr-148 {
  margin-right: 148px;
}

.mt-149 {
  margin-top: 149px;
}

.mb-149 {
  margin-bottom: 149px;
}

.ml-149 {
  margin-left: 149px;
}

.mr-149 {
  margin-right: 149px;
}

.mt-150 {
  margin-top: 150px;
}

.mb-150 {
  margin-bottom: 150px;
}

.ml-150 {
  margin-left: 150px;
}

.mr-150 {
  margin-right: 150px;
}

.mt-151 {
  margin-top: 151px;
}

.mb-151 {
  margin-bottom: 151px;
}

.ml-151 {
  margin-left: 151px;
}

.mr-151 {
  margin-right: 151px;
}

.mt-152 {
  margin-top: 152px;
}

.mb-152 {
  margin-bottom: 152px;
}

.ml-152 {
  margin-left: 152px;
}

.mr-152 {
  margin-right: 152px;
}

.mt-153 {
  margin-top: 153px;
}

.mb-153 {
  margin-bottom: 153px;
}

.ml-153 {
  margin-left: 153px;
}

.mr-153 {
  margin-right: 153px;
}

.mt-154 {
  margin-top: 154px;
}

.mb-154 {
  margin-bottom: 154px;
}

.ml-154 {
  margin-left: 154px;
}

.mr-154 {
  margin-right: 154px;
}

.mt-155 {
  margin-top: 155px;
}

.mb-155 {
  margin-bottom: 155px;
}

.ml-155 {
  margin-left: 155px;
}

.mr-155 {
  margin-right: 155px;
}

.mt-156 {
  margin-top: 156px;
}

.mb-156 {
  margin-bottom: 156px;
}

.ml-156 {
  margin-left: 156px;
}

.mr-156 {
  margin-right: 156px;
}

.mt-157 {
  margin-top: 157px;
}

.mb-157 {
  margin-bottom: 157px;
}

.ml-157 {
  margin-left: 157px;
}

.mr-157 {
  margin-right: 157px;
}

.mt-158 {
  margin-top: 158px;
}

.mb-158 {
  margin-bottom: 158px;
}

.ml-158 {
  margin-left: 158px;
}

.mr-158 {
  margin-right: 158px;
}

.mt-159 {
  margin-top: 159px;
}

.mb-159 {
  margin-bottom: 159px;
}

.ml-159 {
  margin-left: 159px;
}

.mr-159 {
  margin-right: 159px;
}

.mt-160 {
  margin-top: 160px;
}

.mb-160 {
  margin-bottom: 160px;
}

.ml-160 {
  margin-left: 160px;
}

.mr-160 {
  margin-right: 160px;
}

.mt-161 {
  margin-top: 161px;
}

.mb-161 {
  margin-bottom: 161px;
}

.ml-161 {
  margin-left: 161px;
}

.mr-161 {
  margin-right: 161px;
}

.mt-162 {
  margin-top: 162px;
}

.mb-162 {
  margin-bottom: 162px;
}

.ml-162 {
  margin-left: 162px;
}

.mr-162 {
  margin-right: 162px;
}

.mt-163 {
  margin-top: 163px;
}

.mb-163 {
  margin-bottom: 163px;
}

.ml-163 {
  margin-left: 163px;
}

.mr-163 {
  margin-right: 163px;
}

.mt-164 {
  margin-top: 164px;
}

.mb-164 {
  margin-bottom: 164px;
}

.ml-164 {
  margin-left: 164px;
}

.mr-164 {
  margin-right: 164px;
}

.mt-165 {
  margin-top: 165px;
}

.mb-165 {
  margin-bottom: 165px;
}

.ml-165 {
  margin-left: 165px;
}

.mr-165 {
  margin-right: 165px;
}

.mt-166 {
  margin-top: 166px;
}

.mb-166 {
  margin-bottom: 166px;
}

.ml-166 {
  margin-left: 166px;
}

.mr-166 {
  margin-right: 166px;
}

.mt-167 {
  margin-top: 167px;
}

.mb-167 {
  margin-bottom: 167px;
}

.ml-167 {
  margin-left: 167px;
}

.mr-167 {
  margin-right: 167px;
}

.mt-168 {
  margin-top: 168px;
}

.mb-168 {
  margin-bottom: 168px;
}

.ml-168 {
  margin-left: 168px;
}

.mr-168 {
  margin-right: 168px;
}

.mt-169 {
  margin-top: 169px;
}

.mb-169 {
  margin-bottom: 169px;
}

.ml-169 {
  margin-left: 169px;
}

.mr-169 {
  margin-right: 169px;
}

.mt-170 {
  margin-top: 170px;
}

.mb-170 {
  margin-bottom: 170px;
}

.ml-170 {
  margin-left: 170px;
}

.mr-170 {
  margin-right: 170px;
}

.mt-171 {
  margin-top: 171px;
}

.mb-171 {
  margin-bottom: 171px;
}

.ml-171 {
  margin-left: 171px;
}

.mr-171 {
  margin-right: 171px;
}

.mt-172 {
  margin-top: 172px;
}

.mb-172 {
  margin-bottom: 172px;
}

.ml-172 {
  margin-left: 172px;
}

.mr-172 {
  margin-right: 172px;
}

.mt-173 {
  margin-top: 173px;
}

.mb-173 {
  margin-bottom: 173px;
}

.ml-173 {
  margin-left: 173px;
}

.mr-173 {
  margin-right: 173px;
}

.mt-174 {
  margin-top: 174px;
}

.mb-174 {
  margin-bottom: 174px;
}

.ml-174 {
  margin-left: 174px;
}

.mr-174 {
  margin-right: 174px;
}

.mt-175 {
  margin-top: 175px;
}

.mb-175 {
  margin-bottom: 175px;
}

.ml-175 {
  margin-left: 175px;
}

.mr-175 {
  margin-right: 175px;
}

.mt-176 {
  margin-top: 176px;
}

.mb-176 {
  margin-bottom: 176px;
}

.ml-176 {
  margin-left: 176px;
}

.mr-176 {
  margin-right: 176px;
}

.mt-177 {
  margin-top: 177px;
}

.mb-177 {
  margin-bottom: 177px;
}

.ml-177 {
  margin-left: 177px;
}

.mr-177 {
  margin-right: 177px;
}

.mt-178 {
  margin-top: 178px;
}

.mb-178 {
  margin-bottom: 178px;
}

.ml-178 {
  margin-left: 178px;
}

.mr-178 {
  margin-right: 178px;
}

.mt-179 {
  margin-top: 179px;
}

.mb-179 {
  margin-bottom: 179px;
}

.ml-179 {
  margin-left: 179px;
}

.mr-179 {
  margin-right: 179px;
}

.mt-180 {
  margin-top: 180px;
}

.mb-180 {
  margin-bottom: 180px;
}

.ml-180 {
  margin-left: 180px;
}

.mr-180 {
  margin-right: 180px;
}

.mt-181 {
  margin-top: 181px;
}

.mb-181 {
  margin-bottom: 181px;
}

.ml-181 {
  margin-left: 181px;
}

.mr-181 {
  margin-right: 181px;
}

.mt-182 {
  margin-top: 182px;
}

.mb-182 {
  margin-bottom: 182px;
}

.ml-182 {
  margin-left: 182px;
}

.mr-182 {
  margin-right: 182px;
}

.mt-183 {
  margin-top: 183px;
}

.mb-183 {
  margin-bottom: 183px;
}

.ml-183 {
  margin-left: 183px;
}

.mr-183 {
  margin-right: 183px;
}

.mt-184 {
  margin-top: 184px;
}

.mb-184 {
  margin-bottom: 184px;
}

.ml-184 {
  margin-left: 184px;
}

.mr-184 {
  margin-right: 184px;
}

.mt-185 {
  margin-top: 185px;
}

.mb-185 {
  margin-bottom: 185px;
}

.ml-185 {
  margin-left: 185px;
}

.mr-185 {
  margin-right: 185px;
}

.mt-186 {
  margin-top: 186px;
}

.mb-186 {
  margin-bottom: 186px;
}

.ml-186 {
  margin-left: 186px;
}

.mr-186 {
  margin-right: 186px;
}

.mt-187 {
  margin-top: 187px;
}

.mb-187 {
  margin-bottom: 187px;
}

.ml-187 {
  margin-left: 187px;
}

.mr-187 {
  margin-right: 187px;
}

.mt-188 {
  margin-top: 188px;
}

.mb-188 {
  margin-bottom: 188px;
}

.ml-188 {
  margin-left: 188px;
}

.mr-188 {
  margin-right: 188px;
}

.mt-189 {
  margin-top: 189px;
}

.mb-189 {
  margin-bottom: 189px;
}

.ml-189 {
  margin-left: 189px;
}

.mr-189 {
  margin-right: 189px;
}

.mt-190 {
  margin-top: 190px;
}

.mb-190 {
  margin-bottom: 190px;
}

.ml-190 {
  margin-left: 190px;
}

.mr-190 {
  margin-right: 190px;
}

.mt-191 {
  margin-top: 191px;
}

.mb-191 {
  margin-bottom: 191px;
}

.ml-191 {
  margin-left: 191px;
}

.mr-191 {
  margin-right: 191px;
}

.mt-192 {
  margin-top: 192px;
}

.mb-192 {
  margin-bottom: 192px;
}

.ml-192 {
  margin-left: 192px;
}

.mr-192 {
  margin-right: 192px;
}

.mt-193 {
  margin-top: 193px;
}

.mb-193 {
  margin-bottom: 193px;
}

.ml-193 {
  margin-left: 193px;
}

.mr-193 {
  margin-right: 193px;
}

.mt-194 {
  margin-top: 194px;
}

.mb-194 {
  margin-bottom: 194px;
}

.ml-194 {
  margin-left: 194px;
}

.mr-194 {
  margin-right: 194px;
}

.mt-195 {
  margin-top: 195px;
}

.mb-195 {
  margin-bottom: 195px;
}

.ml-195 {
  margin-left: 195px;
}

.mr-195 {
  margin-right: 195px;
}

.mt-196 {
  margin-top: 196px;
}

.mb-196 {
  margin-bottom: 196px;
}

.ml-196 {
  margin-left: 196px;
}

.mr-196 {
  margin-right: 196px;
}

.mt-197 {
  margin-top: 197px;
}

.mb-197 {
  margin-bottom: 197px;
}

.ml-197 {
  margin-left: 197px;
}

.mr-197 {
  margin-right: 197px;
}

.mt-198 {
  margin-top: 198px;
}

.mb-198 {
  margin-bottom: 198px;
}

.ml-198 {
  margin-left: 198px;
}

.mr-198 {
  margin-right: 198px;
}

.mt-199 {
  margin-top: 199px;
}

.mb-199 {
  margin-bottom: 199px;
}

.ml-199 {
  margin-left: 199px;
}

.mr-199 {
  margin-right: 199px;
}

.mt-200 {
  margin-top: 200px;
}

.mb-200 {
  margin-bottom: 200px;
}

.ml-200 {
  margin-left: 200px;
}

.mr-200 {
  margin-right: 200px;
}

.pt-1 {
  padding-top: 1px;
}

.pb-1 {
  padding-bottom: 1px;
}

.pl-1 {
  padding-left: 1px;
}

.pr-1 {
  padding-right: 1px;
}

.pt-2 {
  padding-top: 2px;
}

.pb-2 {
  padding-bottom: 2px;
}

.pl-2 {
  padding-left: 2px;
}

.pr-2 {
  padding-right: 2px;
}

.pt-3 {
  padding-top: 3px;
}

.pb-3 {
  padding-bottom: 3px;
}

.pl-3 {
  padding-left: 3px;
}

.pr-3 {
  padding-right: 3px;
}

.pt-4 {
  padding-top: 4px;
}

.pb-4 {
  padding-bottom: 4px;
}

.pl-4 {
  padding-left: 4px;
}

.pr-4 {
  padding-right: 4px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.pr-5 {
  padding-right: 5px;
}

.pt-6 {
  padding-top: 6px;
}

.pb-6 {
  padding-bottom: 6px;
}

.pl-6 {
  padding-left: 6px;
}

.pr-6 {
  padding-right: 6px;
}

.pt-7 {
  padding-top: 7px;
}

.pb-7 {
  padding-bottom: 7px;
}

.pl-7 {
  padding-left: 7px;
}

.pr-7 {
  padding-right: 7px;
}

.pt-8 {
  padding-top: 8px;
}

.pb-8 {
  padding-bottom: 8px;
}

.pl-8 {
  padding-left: 8px;
}

.pr-8 {
  padding-right: 8px;
}

.pt-9 {
  padding-top: 9px;
}

.pb-9 {
  padding-bottom: 9px;
}

.pl-9 {
  padding-left: 9px;
}

.pr-9 {
  padding-right: 9px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-left: 10px;
}

.pr-10 {
  padding-right: 10px;
}

.pt-11 {
  padding-top: 11px;
}

.pb-11 {
  padding-bottom: 11px;
}

.pl-11 {
  padding-left: 11px;
}

.pr-11 {
  padding-right: 11px;
}

.pt-12 {
  padding-top: 12px;
}

.pb-12 {
  padding-bottom: 12px;
}

.pl-12 {
  padding-left: 12px;
}

.pr-12 {
  padding-right: 12px;
}

.pt-13 {
  padding-top: 13px;
}

.pb-13 {
  padding-bottom: 13px;
}

.pl-13 {
  padding-left: 13px;
}

.pr-13 {
  padding-right: 13px;
}

.pt-14 {
  padding-top: 14px;
}

.pb-14 {
  padding-bottom: 14px;
}

.pl-14 {
  padding-left: 14px;
}

.pr-14 {
  padding-right: 14px;
}

.pt-15 {
  padding-top: 15px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pl-15 {
  padding-left: 15px;
}

.pr-15 {
  padding-right: 15px;
}

.pt-16 {
  padding-top: 16px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pl-16 {
  padding-left: 16px;
}

.pr-16 {
  padding-right: 16px;
}

.pt-17 {
  padding-top: 17px;
}

.pb-17 {
  padding-bottom: 17px;
}

.pl-17 {
  padding-left: 17px;
}

.pr-17 {
  padding-right: 17px;
}

.pt-18 {
  padding-top: 18px;
}

.pb-18 {
  padding-bottom: 18px;
}

.pl-18 {
  padding-left: 18px;
}

.pr-18 {
  padding-right: 18px;
}

.pt-19 {
  padding-top: 19px;
}

.pb-19 {
  padding-bottom: 19px;
}

.pl-19 {
  padding-left: 19px;
}

.pr-19 {
  padding-right: 19px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pl-20 {
  padding-left: 20px;
}

.pr-20 {
  padding-right: 20px;
}

.pt-21 {
  padding-top: 21px;
}

.pb-21 {
  padding-bottom: 21px;
}

.pl-21 {
  padding-left: 21px;
}

.pr-21 {
  padding-right: 21px;
}

.pt-22 {
  padding-top: 22px;
}

.pb-22 {
  padding-bottom: 22px;
}

.pl-22 {
  padding-left: 22px;
}

.pr-22 {
  padding-right: 22px;
}

.pt-23 {
  padding-top: 23px;
}

.pb-23 {
  padding-bottom: 23px;
}

.pl-23 {
  padding-left: 23px;
}

.pr-23 {
  padding-right: 23px;
}

.pt-24 {
  padding-top: 24px;
}

.pb-24 {
  padding-bottom: 24px;
}

.pl-24 {
  padding-left: 24px;
}

.pr-24 {
  padding-right: 24px;
}

.pt-25 {
  padding-top: 25px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pl-25 {
  padding-left: 25px;
}

.pr-25 {
  padding-right: 25px;
}

.pt-26 {
  padding-top: 26px;
}

.pb-26 {
  padding-bottom: 26px;
}

.pl-26 {
  padding-left: 26px;
}

.pr-26 {
  padding-right: 26px;
}

.pt-27 {
  padding-top: 27px;
}

.pb-27 {
  padding-bottom: 27px;
}

.pl-27 {
  padding-left: 27px;
}

.pr-27 {
  padding-right: 27px;
}

.pt-28 {
  padding-top: 28px;
}

.pb-28 {
  padding-bottom: 28px;
}

.pl-28 {
  padding-left: 28px;
}

.pr-28 {
  padding-right: 28px;
}

.pt-29 {
  padding-top: 29px;
}

.pb-29 {
  padding-bottom: 29px;
}

.pl-29 {
  padding-left: 29px;
}

.pr-29 {
  padding-right: 29px;
}

.pt-30 {
  padding-top: 30px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pl-30 {
  padding-left: 30px;
}

.pr-30 {
  padding-right: 30px;
}

.pt-31 {
  padding-top: 31px;
}

.pb-31 {
  padding-bottom: 31px;
}

.pl-31 {
  padding-left: 31px;
}

.pr-31 {
  padding-right: 31px;
}

.pt-32 {
  padding-top: 32px;
}

.pb-32 {
  padding-bottom: 32px;
}

.pl-32 {
  padding-left: 32px;
}

.pr-32 {
  padding-right: 32px;
}

.pt-33 {
  padding-top: 33px;
}

.pb-33 {
  padding-bottom: 33px;
}

.pl-33 {
  padding-left: 33px;
}

.pr-33 {
  padding-right: 33px;
}

.pt-34 {
  padding-top: 34px;
}

.pb-34 {
  padding-bottom: 34px;
}

.pl-34 {
  padding-left: 34px;
}

.pr-34 {
  padding-right: 34px;
}

.pt-35 {
  padding-top: 35px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pl-35 {
  padding-left: 35px;
}

.pr-35 {
  padding-right: 35px;
}

.pt-36 {
  padding-top: 36px;
}

.pb-36 {
  padding-bottom: 36px;
}

.pl-36 {
  padding-left: 36px;
}

.pr-36 {
  padding-right: 36px;
}

.pt-37 {
  padding-top: 37px;
}

.pb-37 {
  padding-bottom: 37px;
}

.pl-37 {
  padding-left: 37px;
}

.pr-37 {
  padding-right: 37px;
}

.pt-38 {
  padding-top: 38px;
}

.pb-38 {
  padding-bottom: 38px;
}

.pl-38 {
  padding-left: 38px;
}

.pr-38 {
  padding-right: 38px;
}

.pt-39 {
  padding-top: 39px;
}

.pb-39 {
  padding-bottom: 39px;
}

.pl-39 {
  padding-left: 39px;
}

.pr-39 {
  padding-right: 39px;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pl-40 {
  padding-left: 40px;
}

.pr-40 {
  padding-right: 40px;
}

.pt-41 {
  padding-top: 41px;
}

.pb-41 {
  padding-bottom: 41px;
}

.pl-41 {
  padding-left: 41px;
}

.pr-41 {
  padding-right: 41px;
}

.pt-42 {
  padding-top: 42px;
}

.pb-42 {
  padding-bottom: 42px;
}

.pl-42 {
  padding-left: 42px;
}

.pr-42 {
  padding-right: 42px;
}

.pt-43 {
  padding-top: 43px;
}

.pb-43 {
  padding-bottom: 43px;
}

.pl-43 {
  padding-left: 43px;
}

.pr-43 {
  padding-right: 43px;
}

.pt-44 {
  padding-top: 44px;
}

.pb-44 {
  padding-bottom: 44px;
}

.pl-44 {
  padding-left: 44px;
}

.pr-44 {
  padding-right: 44px;
}

.pt-45 {
  padding-top: 45px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pl-45 {
  padding-left: 45px;
}

.pr-45 {
  padding-right: 45px;
}

.pt-46 {
  padding-top: 46px;
}

.pb-46 {
  padding-bottom: 46px;
}

.pl-46 {
  padding-left: 46px;
}

.pr-46 {
  padding-right: 46px;
}

.pt-47 {
  padding-top: 47px;
}

.pb-47 {
  padding-bottom: 47px;
}

.pl-47 {
  padding-left: 47px;
}

.pr-47 {
  padding-right: 47px;
}

.pt-48 {
  padding-top: 48px;
}

.pb-48 {
  padding-bottom: 48px;
}

.pl-48 {
  padding-left: 48px;
}

.pr-48 {
  padding-right: 48px;
}

.pt-49 {
  padding-top: 49px;
}

.pb-49 {
  padding-bottom: 49px;
}

.pl-49 {
  padding-left: 49px;
}

.pr-49 {
  padding-right: 49px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pl-50 {
  padding-left: 50px;
}

.pr-50 {
  padding-right: 50px;
}

.pt-51 {
  padding-top: 51px;
}

.pb-51 {
  padding-bottom: 51px;
}

.pl-51 {
  padding-left: 51px;
}

.pr-51 {
  padding-right: 51px;
}

.pt-52 {
  padding-top: 52px;
}

.pb-52 {
  padding-bottom: 52px;
}

.pl-52 {
  padding-left: 52px;
}

.pr-52 {
  padding-right: 52px;
}

.pt-53 {
  padding-top: 53px;
}

.pb-53 {
  padding-bottom: 53px;
}

.pl-53 {
  padding-left: 53px;
}

.pr-53 {
  padding-right: 53px;
}

.pt-54 {
  padding-top: 54px;
}

.pb-54 {
  padding-bottom: 54px;
}

.pl-54 {
  padding-left: 54px;
}

.pr-54 {
  padding-right: 54px;
}

.pt-55 {
  padding-top: 55px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pl-55 {
  padding-left: 55px;
}

.pr-55 {
  padding-right: 55px;
}

.pt-56 {
  padding-top: 56px;
}

.pb-56 {
  padding-bottom: 56px;
}

.pl-56 {
  padding-left: 56px;
}

.pr-56 {
  padding-right: 56px;
}

.pt-57 {
  padding-top: 57px;
}

.pb-57 {
  padding-bottom: 57px;
}

.pl-57 {
  padding-left: 57px;
}

.pr-57 {
  padding-right: 57px;
}

.pt-58 {
  padding-top: 58px;
}

.pb-58 {
  padding-bottom: 58px;
}

.pl-58 {
  padding-left: 58px;
}

.pr-58 {
  padding-right: 58px;
}

.pt-59 {
  padding-top: 59px;
}

.pb-59 {
  padding-bottom: 59px;
}

.pl-59 {
  padding-left: 59px;
}

.pr-59 {
  padding-right: 59px;
}

.pt-60 {
  padding-top: 60px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-60 {
  padding-left: 60px;
}

.pr-60 {
  padding-right: 60px;
}

.pt-61 {
  padding-top: 61px;
}

.pb-61 {
  padding-bottom: 61px;
}

.pl-61 {
  padding-left: 61px;
}

.pr-61 {
  padding-right: 61px;
}

.pt-62 {
  padding-top: 62px;
}

.pb-62 {
  padding-bottom: 62px;
}

.pl-62 {
  padding-left: 62px;
}

.pr-62 {
  padding-right: 62px;
}

.pt-63 {
  padding-top: 63px;
}

.pb-63 {
  padding-bottom: 63px;
}

.pl-63 {
  padding-left: 63px;
}

.pr-63 {
  padding-right: 63px;
}

.pt-64 {
  padding-top: 64px;
}

.pb-64 {
  padding-bottom: 64px;
}

.pl-64 {
  padding-left: 64px;
}

.pr-64 {
  padding-right: 64px;
}

.pt-65 {
  padding-top: 65px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pl-65 {
  padding-left: 65px;
}

.pr-65 {
  padding-right: 65px;
}

.pt-66 {
  padding-top: 66px;
}

.pb-66 {
  padding-bottom: 66px;
}

.pl-66 {
  padding-left: 66px;
}

.pr-66 {
  padding-right: 66px;
}

.pt-67 {
  padding-top: 67px;
}

.pb-67 {
  padding-bottom: 67px;
}

.pl-67 {
  padding-left: 67px;
}

.pr-67 {
  padding-right: 67px;
}

.pt-68 {
  padding-top: 68px;
}

.pb-68 {
  padding-bottom: 68px;
}

.pl-68 {
  padding-left: 68px;
}

.pr-68 {
  padding-right: 68px;
}

.pt-69 {
  padding-top: 69px;
}

.pb-69 {
  padding-bottom: 69px;
}

.pl-69 {
  padding-left: 69px;
}

.pr-69 {
  padding-right: 69px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pl-70 {
  padding-left: 70px;
}

.pr-70 {
  padding-right: 70px;
}

.pt-71 {
  padding-top: 71px;
}

.pb-71 {
  padding-bottom: 71px;
}

.pl-71 {
  padding-left: 71px;
}

.pr-71 {
  padding-right: 71px;
}

.pt-72 {
  padding-top: 72px;
}

.pb-72 {
  padding-bottom: 72px;
}

.pl-72 {
  padding-left: 72px;
}

.pr-72 {
  padding-right: 72px;
}

.pt-73 {
  padding-top: 73px;
}

.pb-73 {
  padding-bottom: 73px;
}

.pl-73 {
  padding-left: 73px;
}

.pr-73 {
  padding-right: 73px;
}

.pt-74 {
  padding-top: 74px;
}

.pb-74 {
  padding-bottom: 74px;
}

.pl-74 {
  padding-left: 74px;
}

.pr-74 {
  padding-right: 74px;
}

.pt-75 {
  padding-top: 75px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pl-75 {
  padding-left: 75px;
}

.pr-75 {
  padding-right: 75px;
}

.pt-76 {
  padding-top: 76px;
}

.pb-76 {
  padding-bottom: 76px;
}

.pl-76 {
  padding-left: 76px;
}

.pr-76 {
  padding-right: 76px;
}

.pt-77 {
  padding-top: 77px;
}

.pb-77 {
  padding-bottom: 77px;
}

.pl-77 {
  padding-left: 77px;
}

.pr-77 {
  padding-right: 77px;
}

.pt-78 {
  padding-top: 78px;
}

.pb-78 {
  padding-bottom: 78px;
}

.pl-78 {
  padding-left: 78px;
}

.pr-78 {
  padding-right: 78px;
}

.pt-79 {
  padding-top: 79px;
}

.pb-79 {
  padding-bottom: 79px;
}

.pl-79 {
  padding-left: 79px;
}

.pr-79 {
  padding-right: 79px;
}

.pt-80 {
  padding-top: 80px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pl-80 {
  padding-left: 80px;
}

.pr-80 {
  padding-right: 80px;
}

.pt-81 {
  padding-top: 81px;
}

.pb-81 {
  padding-bottom: 81px;
}

.pl-81 {
  padding-left: 81px;
}

.pr-81 {
  padding-right: 81px;
}

.pt-82 {
  padding-top: 82px;
}

.pb-82 {
  padding-bottom: 82px;
}

.pl-82 {
  padding-left: 82px;
}

.pr-82 {
  padding-right: 82px;
}

.pt-83 {
  padding-top: 83px;
}

.pb-83 {
  padding-bottom: 83px;
}

.pl-83 {
  padding-left: 83px;
}

.pr-83 {
  padding-right: 83px;
}

.pt-84 {
  padding-top: 84px;
}

.pb-84 {
  padding-bottom: 84px;
}

.pl-84 {
  padding-left: 84px;
}

.pr-84 {
  padding-right: 84px;
}

.pt-85 {
  padding-top: 85px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pl-85 {
  padding-left: 85px;
}

.pr-85 {
  padding-right: 85px;
}

.pt-86 {
  padding-top: 86px;
}

.pb-86 {
  padding-bottom: 86px;
}

.pl-86 {
  padding-left: 86px;
}

.pr-86 {
  padding-right: 86px;
}

.pt-87 {
  padding-top: 87px;
}

.pb-87 {
  padding-bottom: 87px;
}

.pl-87 {
  padding-left: 87px;
}

.pr-87 {
  padding-right: 87px;
}

.pt-88 {
  padding-top: 88px;
}

.pb-88 {
  padding-bottom: 88px;
}

.pl-88 {
  padding-left: 88px;
}

.pr-88 {
  padding-right: 88px;
}

.pt-89 {
  padding-top: 89px;
}

.pb-89 {
  padding-bottom: 89px;
}

.pl-89 {
  padding-left: 89px;
}

.pr-89 {
  padding-right: 89px;
}

.pt-90 {
  padding-top: 90px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pl-90 {
  padding-left: 90px;
}

.pr-90 {
  padding-right: 90px;
}

.pt-91 {
  padding-top: 91px;
}

.pb-91 {
  padding-bottom: 91px;
}

.pl-91 {
  padding-left: 91px;
}

.pr-91 {
  padding-right: 91px;
}

.pt-92 {
  padding-top: 92px;
}

.pb-92 {
  padding-bottom: 92px;
}

.pl-92 {
  padding-left: 92px;
}

.pr-92 {
  padding-right: 92px;
}

.pt-93 {
  padding-top: 93px;
}

.pb-93 {
  padding-bottom: 93px;
}

.pl-93 {
  padding-left: 93px;
}

.pr-93 {
  padding-right: 93px;
}

.pt-94 {
  padding-top: 94px;
}

.pb-94 {
  padding-bottom: 94px;
}

.pl-94 {
  padding-left: 94px;
}

.pr-94 {
  padding-right: 94px;
}

.pt-95 {
  padding-top: 95px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pl-95 {
  padding-left: 95px;
}

.pr-95 {
  padding-right: 95px;
}

.pt-96 {
  padding-top: 96px;
}

.pb-96 {
  padding-bottom: 96px;
}

.pl-96 {
  padding-left: 96px;
}

.pr-96 {
  padding-right: 96px;
}

.pt-97 {
  padding-top: 97px;
}

.pb-97 {
  padding-bottom: 97px;
}

.pl-97 {
  padding-left: 97px;
}

.pr-97 {
  padding-right: 97px;
}

.pt-98 {
  padding-top: 98px;
}

.pb-98 {
  padding-bottom: 98px;
}

.pl-98 {
  padding-left: 98px;
}

.pr-98 {
  padding-right: 98px;
}

.pt-99 {
  padding-top: 99px;
}

.pb-99 {
  padding-bottom: 99px;
}

.pl-99 {
  padding-left: 99px;
}

.pr-99 {
  padding-right: 99px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pl-100 {
  padding-left: 100px;
}

.pr-100 {
  padding-right: 100px;
}

.pt-101 {
  padding-top: 101px;
}

.pb-101 {
  padding-bottom: 101px;
}

.pl-101 {
  padding-left: 101px;
}

.pr-101 {
  padding-right: 101px;
}

.pt-102 {
  padding-top: 102px;
}

.pb-102 {
  padding-bottom: 102px;
}

.pl-102 {
  padding-left: 102px;
}

.pr-102 {
  padding-right: 102px;
}

.pt-103 {
  padding-top: 103px;
}

.pb-103 {
  padding-bottom: 103px;
}

.pl-103 {
  padding-left: 103px;
}

.pr-103 {
  padding-right: 103px;
}

.pt-104 {
  padding-top: 104px;
}

.pb-104 {
  padding-bottom: 104px;
}

.pl-104 {
  padding-left: 104px;
}

.pr-104 {
  padding-right: 104px;
}

.pt-105 {
  padding-top: 105px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pl-105 {
  padding-left: 105px;
}

.pr-105 {
  padding-right: 105px;
}

.pt-106 {
  padding-top: 106px;
}

.pb-106 {
  padding-bottom: 106px;
}

.pl-106 {
  padding-left: 106px;
}

.pr-106 {
  padding-right: 106px;
}

.pt-107 {
  padding-top: 107px;
}

.pb-107 {
  padding-bottom: 107px;
}

.pl-107 {
  padding-left: 107px;
}

.pr-107 {
  padding-right: 107px;
}

.pt-108 {
  padding-top: 108px;
}

.pb-108 {
  padding-bottom: 108px;
}

.pl-108 {
  padding-left: 108px;
}

.pr-108 {
  padding-right: 108px;
}

.pt-109 {
  padding-top: 109px;
}

.pb-109 {
  padding-bottom: 109px;
}

.pl-109 {
  padding-left: 109px;
}

.pr-109 {
  padding-right: 109px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pl-110 {
  padding-left: 110px;
}

.pr-110 {
  padding-right: 110px;
}

.pt-111 {
  padding-top: 111px;
}

.pb-111 {
  padding-bottom: 111px;
}

.pl-111 {
  padding-left: 111px;
}

.pr-111 {
  padding-right: 111px;
}

.pt-112 {
  padding-top: 112px;
}

.pb-112 {
  padding-bottom: 112px;
}

.pl-112 {
  padding-left: 112px;
}

.pr-112 {
  padding-right: 112px;
}

.pt-113 {
  padding-top: 113px;
}

.pb-113 {
  padding-bottom: 113px;
}

.pl-113 {
  padding-left: 113px;
}

.pr-113 {
  padding-right: 113px;
}

.pt-114 {
  padding-top: 114px;
}

.pb-114 {
  padding-bottom: 114px;
}

.pl-114 {
  padding-left: 114px;
}

.pr-114 {
  padding-right: 114px;
}

.pt-115 {
  padding-top: 115px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pl-115 {
  padding-left: 115px;
}

.pr-115 {
  padding-right: 115px;
}

.pt-116 {
  padding-top: 116px;
}

.pb-116 {
  padding-bottom: 116px;
}

.pl-116 {
  padding-left: 116px;
}

.pr-116 {
  padding-right: 116px;
}

.pt-117 {
  padding-top: 117px;
}

.pb-117 {
  padding-bottom: 117px;
}

.pl-117 {
  padding-left: 117px;
}

.pr-117 {
  padding-right: 117px;
}

.pt-118 {
  padding-top: 118px;
}

.pb-118 {
  padding-bottom: 118px;
}

.pl-118 {
  padding-left: 118px;
}

.pr-118 {
  padding-right: 118px;
}

.pt-119 {
  padding-top: 119px;
}

.pb-119 {
  padding-bottom: 119px;
}

.pl-119 {
  padding-left: 119px;
}

.pr-119 {
  padding-right: 119px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-120 {
  padding-left: 120px;
}

.pr-120 {
  padding-right: 120px;
}

.pt-121 {
  padding-top: 121px;
}

.pb-121 {
  padding-bottom: 121px;
}

.pl-121 {
  padding-left: 121px;
}

.pr-121 {
  padding-right: 121px;
}

.pt-122 {
  padding-top: 122px;
}

.pb-122 {
  padding-bottom: 122px;
}

.pl-122 {
  padding-left: 122px;
}

.pr-122 {
  padding-right: 122px;
}

.pt-123 {
  padding-top: 123px;
}

.pb-123 {
  padding-bottom: 123px;
}

.pl-123 {
  padding-left: 123px;
}

.pr-123 {
  padding-right: 123px;
}

.pt-124 {
  padding-top: 124px;
}

.pb-124 {
  padding-bottom: 124px;
}

.pl-124 {
  padding-left: 124px;
}

.pr-124 {
  padding-right: 124px;
}

.pt-125 {
  padding-top: 125px;
}

.pb-125 {
  padding-bottom: 125px;
}

.pl-125 {
  padding-left: 125px;
}

.pr-125 {
  padding-right: 125px;
}

.pt-126 {
  padding-top: 126px;
}

.pb-126 {
  padding-bottom: 126px;
}

.pl-126 {
  padding-left: 126px;
}

.pr-126 {
  padding-right: 126px;
}

.pt-127 {
  padding-top: 127px;
}

.pb-127 {
  padding-bottom: 127px;
}

.pl-127 {
  padding-left: 127px;
}

.pr-127 {
  padding-right: 127px;
}

.pt-128 {
  padding-top: 128px;
}

.pb-128 {
  padding-bottom: 128px;
}

.pl-128 {
  padding-left: 128px;
}

.pr-128 {
  padding-right: 128px;
}

.pt-129 {
  padding-top: 129px;
}

.pb-129 {
  padding-bottom: 129px;
}

.pl-129 {
  padding-left: 129px;
}

.pr-129 {
  padding-right: 129px;
}

.pt-130 {
  padding-top: 130px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pl-130 {
  padding-left: 130px;
}

.pr-130 {
  padding-right: 130px;
}

.pt-131 {
  padding-top: 131px;
}

.pb-131 {
  padding-bottom: 131px;
}

.pl-131 {
  padding-left: 131px;
}

.pr-131 {
  padding-right: 131px;
}

.pt-132 {
  padding-top: 132px;
}

.pb-132 {
  padding-bottom: 132px;
}

.pl-132 {
  padding-left: 132px;
}

.pr-132 {
  padding-right: 132px;
}

.pt-133 {
  padding-top: 133px;
}

.pb-133 {
  padding-bottom: 133px;
}

.pl-133 {
  padding-left: 133px;
}

.pr-133 {
  padding-right: 133px;
}

.pt-134 {
  padding-top: 134px;
}

.pb-134 {
  padding-bottom: 134px;
}

.pl-134 {
  padding-left: 134px;
}

.pr-134 {
  padding-right: 134px;
}

.pt-135 {
  padding-top: 135px;
}

.pb-135 {
  padding-bottom: 135px;
}

.pl-135 {
  padding-left: 135px;
}

.pr-135 {
  padding-right: 135px;
}

.pt-136 {
  padding-top: 136px;
}

.pb-136 {
  padding-bottom: 136px;
}

.pl-136 {
  padding-left: 136px;
}

.pr-136 {
  padding-right: 136px;
}

.pt-137 {
  padding-top: 137px;
}

.pb-137 {
  padding-bottom: 137px;
}

.pl-137 {
  padding-left: 137px;
}

.pr-137 {
  padding-right: 137px;
}

.pt-138 {
  padding-top: 138px;
}

.pb-138 {
  padding-bottom: 138px;
}

.pl-138 {
  padding-left: 138px;
}

.pr-138 {
  padding-right: 138px;
}

.pt-139 {
  padding-top: 139px;
}

.pb-139 {
  padding-bottom: 139px;
}

.pl-139 {
  padding-left: 139px;
}

.pr-139 {
  padding-right: 139px;
}

.pt-140 {
  padding-top: 140px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pl-140 {
  padding-left: 140px;
}

.pr-140 {
  padding-right: 140px;
}

.pt-141 {
  padding-top: 141px;
}

.pb-141 {
  padding-bottom: 141px;
}

.pl-141 {
  padding-left: 141px;
}

.pr-141 {
  padding-right: 141px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-142 {
  padding-bottom: 142px;
}

.pl-142 {
  padding-left: 142px;
}

.pr-142 {
  padding-right: 142px;
}

.pt-143 {
  padding-top: 143px;
}

.pb-143 {
  padding-bottom: 143px;
}

.pl-143 {
  padding-left: 143px;
}

.pr-143 {
  padding-right: 143px;
}

.pt-144 {
  padding-top: 144px;
}

.pb-144 {
  padding-bottom: 144px;
}

.pl-144 {
  padding-left: 144px;
}

.pr-144 {
  padding-right: 144px;
}

.pt-145 {
  padding-top: 145px;
}

.pb-145 {
  padding-bottom: 145px;
}

.pl-145 {
  padding-left: 145px;
}

.pr-145 {
  padding-right: 145px;
}

.pt-146 {
  padding-top: 146px;
}

.pb-146 {
  padding-bottom: 146px;
}

.pl-146 {
  padding-left: 146px;
}

.pr-146 {
  padding-right: 146px;
}

.pt-147 {
  padding-top: 147px;
}

.pb-147 {
  padding-bottom: 147px;
}

.pl-147 {
  padding-left: 147px;
}

.pr-147 {
  padding-right: 147px;
}

.pt-148 {
  padding-top: 148px;
}

.pb-148 {
  padding-bottom: 148px;
}

.pl-148 {
  padding-left: 148px;
}

.pr-148 {
  padding-right: 148px;
}

.pt-149 {
  padding-top: 149px;
}

.pb-149 {
  padding-bottom: 149px;
}

.pl-149 {
  padding-left: 149px;
}

.pr-149 {
  padding-right: 149px;
}

.pt-150 {
  padding-top: 150px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pl-150 {
  padding-left: 150px;
}

.pr-150 {
  padding-right: 150px;
}

.pt-151 {
  padding-top: 151px;
}

.pb-151 {
  padding-bottom: 151px;
}

.pl-151 {
  padding-left: 151px;
}

.pr-151 {
  padding-right: 151px;
}

.pt-152 {
  padding-top: 152px;
}

.pb-152 {
  padding-bottom: 152px;
}

.pl-152 {
  padding-left: 152px;
}

.pr-152 {
  padding-right: 152px;
}

.pt-153 {
  padding-top: 153px;
}

.pb-153 {
  padding-bottom: 153px;
}

.pl-153 {
  padding-left: 153px;
}

.pr-153 {
  padding-right: 153px;
}

.pt-154 {
  padding-top: 154px;
}

.pb-154 {
  padding-bottom: 154px;
}

.pl-154 {
  padding-left: 154px;
}

.pr-154 {
  padding-right: 154px;
}

.pt-155 {
  padding-top: 155px;
}

.pb-155 {
  padding-bottom: 155px;
}

.pl-155 {
  padding-left: 155px;
}

.pr-155 {
  padding-right: 155px;
}

.pt-156 {
  padding-top: 156px;
}

.pb-156 {
  padding-bottom: 156px;
}

.pl-156 {
  padding-left: 156px;
}

.pr-156 {
  padding-right: 156px;
}

.pt-157 {
  padding-top: 157px;
}

.pb-157 {
  padding-bottom: 157px;
}

.pl-157 {
  padding-left: 157px;
}

.pr-157 {
  padding-right: 157px;
}

.pt-158 {
  padding-top: 158px;
}

.pb-158 {
  padding-bottom: 158px;
}

.pl-158 {
  padding-left: 158px;
}

.pr-158 {
  padding-right: 158px;
}

.pt-159 {
  padding-top: 159px;
}

.pb-159 {
  padding-bottom: 159px;
}

.pl-159 {
  padding-left: 159px;
}

.pr-159 {
  padding-right: 159px;
}

.pt-160 {
  padding-top: 160px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pl-160 {
  padding-left: 160px;
}

.pr-160 {
  padding-right: 160px;
}

.pt-161 {
  padding-top: 161px;
}

.pb-161 {
  padding-bottom: 161px;
}

.pl-161 {
  padding-left: 161px;
}

.pr-161 {
  padding-right: 161px;
}

.pt-162 {
  padding-top: 162px;
}

.pb-162 {
  padding-bottom: 162px;
}

.pl-162 {
  padding-left: 162px;
}

.pr-162 {
  padding-right: 162px;
}

.pt-163 {
  padding-top: 163px;
}

.pb-163 {
  padding-bottom: 163px;
}

.pl-163 {
  padding-left: 163px;
}

.pr-163 {
  padding-right: 163px;
}

.pt-164 {
  padding-top: 164px;
}

.pb-164 {
  padding-bottom: 164px;
}

.pl-164 {
  padding-left: 164px;
}

.pr-164 {
  padding-right: 164px;
}

.pt-165 {
  padding-top: 165px;
}

.pb-165 {
  padding-bottom: 165px;
}

.pl-165 {
  padding-left: 165px;
}

.pr-165 {
  padding-right: 165px;
}

.pt-166 {
  padding-top: 166px;
}

.pb-166 {
  padding-bottom: 166px;
}

.pl-166 {
  padding-left: 166px;
}

.pr-166 {
  padding-right: 166px;
}

.pt-167 {
  padding-top: 167px;
}

.pb-167 {
  padding-bottom: 167px;
}

.pl-167 {
  padding-left: 167px;
}

.pr-167 {
  padding-right: 167px;
}

.pt-168 {
  padding-top: 168px;
}

.pb-168 {
  padding-bottom: 168px;
}

.pl-168 {
  padding-left: 168px;
}

.pr-168 {
  padding-right: 168px;
}

.pt-169 {
  padding-top: 169px;
}

.pb-169 {
  padding-bottom: 169px;
}

.pl-169 {
  padding-left: 169px;
}

.pr-169 {
  padding-right: 169px;
}

.pt-170 {
  padding-top: 170px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pl-170 {
  padding-left: 170px;
}

.pr-170 {
  padding-right: 170px;
}

.pt-171 {
  padding-top: 171px;
}

.pb-171 {
  padding-bottom: 171px;
}

.pl-171 {
  padding-left: 171px;
}

.pr-171 {
  padding-right: 171px;
}

.pt-172 {
  padding-top: 172px;
}

.pb-172 {
  padding-bottom: 172px;
}

.pl-172 {
  padding-left: 172px;
}

.pr-172 {
  padding-right: 172px;
}

.pt-173 {
  padding-top: 173px;
}

.pb-173 {
  padding-bottom: 173px;
}

.pl-173 {
  padding-left: 173px;
}

.pr-173 {
  padding-right: 173px;
}

.pt-174 {
  padding-top: 174px;
}

.pb-174 {
  padding-bottom: 174px;
}

.pl-174 {
  padding-left: 174px;
}

.pr-174 {
  padding-right: 174px;
}

.pt-175 {
  padding-top: 175px;
}

.pb-175 {
  padding-bottom: 175px;
}

.pl-175 {
  padding-left: 175px;
}

.pr-175 {
  padding-right: 175px;
}

.pt-176 {
  padding-top: 176px;
}

.pb-176 {
  padding-bottom: 176px;
}

.pl-176 {
  padding-left: 176px;
}

.pr-176 {
  padding-right: 176px;
}

.pt-177 {
  padding-top: 177px;
}

.pb-177 {
  padding-bottom: 177px;
}

.pl-177 {
  padding-left: 177px;
}

.pr-177 {
  padding-right: 177px;
}

.pt-178 {
  padding-top: 178px;
}

.pb-178 {
  padding-bottom: 178px;
}

.pl-178 {
  padding-left: 178px;
}

.pr-178 {
  padding-right: 178px;
}

.pt-179 {
  padding-top: 179px;
}

.pb-179 {
  padding-bottom: 179px;
}

.pl-179 {
  padding-left: 179px;
}

.pr-179 {
  padding-right: 179px;
}

.pt-180 {
  padding-top: 180px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pl-180 {
  padding-left: 180px;
}

.pr-180 {
  padding-right: 180px;
}

.pt-181 {
  padding-top: 181px;
}

.pb-181 {
  padding-bottom: 181px;
}

.pl-181 {
  padding-left: 181px;
}

.pr-181 {
  padding-right: 181px;
}

.pt-182 {
  padding-top: 182px;
}

.pb-182 {
  padding-bottom: 182px;
}

.pl-182 {
  padding-left: 182px;
}

.pr-182 {
  padding-right: 182px;
}

.pt-183 {
  padding-top: 183px;
}

.pb-183 {
  padding-bottom: 183px;
}

.pl-183 {
  padding-left: 183px;
}

.pr-183 {
  padding-right: 183px;
}

.pt-184 {
  padding-top: 184px;
}

.pb-184 {
  padding-bottom: 184px;
}

.pl-184 {
  padding-left: 184px;
}

.pr-184 {
  padding-right: 184px;
}

.pt-185 {
  padding-top: 185px;
}

.pb-185 {
  padding-bottom: 185px;
}

.pl-185 {
  padding-left: 185px;
}

.pr-185 {
  padding-right: 185px;
}

.pt-186 {
  padding-top: 186px;
}

.pb-186 {
  padding-bottom: 186px;
}

.pl-186 {
  padding-left: 186px;
}

.pr-186 {
  padding-right: 186px;
}

.pt-187 {
  padding-top: 187px;
}

.pb-187 {
  padding-bottom: 187px;
}

.pl-187 {
  padding-left: 187px;
}

.pr-187 {
  padding-right: 187px;
}

.pt-188 {
  padding-top: 188px;
}

.pb-188 {
  padding-bottom: 188px;
}

.pl-188 {
  padding-left: 188px;
}

.pr-188 {
  padding-right: 188px;
}

.pt-189 {
  padding-top: 189px;
}

.pb-189 {
  padding-bottom: 189px;
}

.pl-189 {
  padding-left: 189px;
}

.pr-189 {
  padding-right: 189px;
}

.pt-190 {
  padding-top: 190px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pl-190 {
  padding-left: 190px;
}

.pr-190 {
  padding-right: 190px;
}

.pt-191 {
  padding-top: 191px;
}

.pb-191 {
  padding-bottom: 191px;
}

.pl-191 {
  padding-left: 191px;
}

.pr-191 {
  padding-right: 191px;
}

.pt-192 {
  padding-top: 192px;
}

.pb-192 {
  padding-bottom: 192px;
}

.pl-192 {
  padding-left: 192px;
}

.pr-192 {
  padding-right: 192px;
}

.pt-193 {
  padding-top: 193px;
}

.pb-193 {
  padding-bottom: 193px;
}

.pl-193 {
  padding-left: 193px;
}

.pr-193 {
  padding-right: 193px;
}

.pt-194 {
  padding-top: 194px;
}

.pb-194 {
  padding-bottom: 194px;
}

.pl-194 {
  padding-left: 194px;
}

.pr-194 {
  padding-right: 194px;
}

.pt-195 {
  padding-top: 195px;
}

.pb-195 {
  padding-bottom: 195px;
}

.pl-195 {
  padding-left: 195px;
}

.pr-195 {
  padding-right: 195px;
}

.pt-196 {
  padding-top: 196px;
}

.pb-196 {
  padding-bottom: 196px;
}

.pl-196 {
  padding-left: 196px;
}

.pr-196 {
  padding-right: 196px;
}

.pt-197 {
  padding-top: 197px;
}

.pb-197 {
  padding-bottom: 197px;
}

.pl-197 {
  padding-left: 197px;
}

.pr-197 {
  padding-right: 197px;
}

.pt-198 {
  padding-top: 198px;
}

.pb-198 {
  padding-bottom: 198px;
}

.pl-198 {
  padding-left: 198px;
}

.pr-198 {
  padding-right: 198px;
}

.pt-199 {
  padding-top: 199px;
}

.pb-199 {
  padding-bottom: 199px;
}

.pl-199 {
  padding-left: 199px;
}

.pr-199 {
  padding-right: 199px;
}

.pt-200 {
  padding-top: 200px;
}

.pb-200 {
  padding-bottom: 200px;
}

.pl-200 {
  padding-left: 200px;
}

.pr-200 {
  padding-right: 200px;
}

:root {
  --edc-body-font: "Outfit", sans-serif;
  --td-ff-fontawesome: "Font Awesome 6 Free";
  --td-white: hsl(0, 0%, 100%);
  --td-black: hsl(0, 0%, 0%);
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);
  --edc-heading: #093E63;
  --edc-primary: #07BBA8;
  --edc-text-primary:#6B8BA1;
  --edc-ternary:rgba(7, 187, 168, 0.50);
  --edc-bg-1:#FAFAFA;
}

/*---------------------------------
/*  1.2 spacing
---------------------------------*/
.edc-sec-space-paddingY {
  padding-block-start: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
  padding-block-end: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
}

.edc-sec-space-paddingT {
  padding-block-start: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
}

.edc-sec-space-paddingB {
  padding-block-end: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
}

.edc-sec-space-marginY {
  margin-block-start: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
  margin-block-end: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
}

.edc-sec-space-marginT {
  margin-block-start: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
}

.edc-sec-space-marginB {
  margin-block-end: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
}

.edc-title_mt {
  margin-block-start: clamp(1.5625rem, 6.5vw, 3.125rem);
}

.edc-title_mb {
  margin-block-end: clamp(1.5625rem, 6.5vw, 3.125rem);
}

/*----------------------------------------*/
/*   1.3 typography
/*----------------------------------------*/
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body.edoccare {
  font-family: var(--edc-body-font);
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--td-heading);
  margin-top: 0px;
  line-height: 1.3;
  margin-bottom: 0;
  word-break: break-word;
}

p {
  margin-bottom: 0;
}

a {
  text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

.o-x-clip {
  overflow-x: clip;
}

ul {
  margin-bottom: 0;
  margin-left: 0;
  padding-left: 0;
  list-style-type: none;
}

img {
  max-width: 100%;
  object-fit: cover;
}

button {
  font-family: var(--td-ff-body) !important;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
  font-family: var(--td-ff-body);
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

hr:not([size]) {
  border-color: var(--td-card-bg-1);
  opacity: 1;
  border-width: 1px;
}

*::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

*::-moz-placeholder {
  opacity: 1;
  font-size: 14px;
}

*::placeholder {
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
}

/*---------------------------------
  1.2 Common Classes
---------------------------------*/
.w-img img {
  width: 100%;
}

.m-img img {
  max-width: 100%;
}

.fix {
  overflow: hidden;
}

.clear {
  clear: both;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.z-index-1 {
  z-index: 1;
}

.z-index-11 {
  z-index: 11;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.position-absolute {
  position: absolute;
}

.include-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.hr-1 {
  border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
  overflow-x: clip;
}

.o-visible {
  overflow: visible;
}

.valign {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
}

/*----------------------------------------
  Bootstrap customize
-----------------------------------------*/
@media (min-width: 1601px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1350px;
  }
}
/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1770px) and (max-width: 4000px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 750px;
  }
}

.mfp-close {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.mfp-close:hover {
  color: var(--td-white);
}
.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: var(--td-ff-fontawesome);
  font-size: 20px;
  font-weight: 900;
  inset-inline-end: -5px;
  margin-top: -16px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-close::after {
    inset-inline-end: 15px;
    margin-top: -30px;
  }
}

.home-2 {
  background-color: var(--td-bg);
}

/*----------------------------------------*/
/*  Button
/*----------------------------------------*/
.edoc-primary-btn {
  all: unset;
  display: inline-flex;
  height: 44px;
  align-items: center;
  position: relative;
  padding: 0 24px;
  border: #07BBA8 solid 1px;
  border-radius: 4px;
  color: var(--td-white);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  overflow: hidden;
  transition: border 700ms, color 600ms;
  user-select: none;
}
@media (max-width: 767px) {
  .edoc-primary-btn {
    height: 38px;
    font-size: 12px;
  }
}
.edoc-primary-btn span {
  display: block;
  z-index: 1;
}
.edoc-primary-btn:hover, .edoc-primary-btn.active {
  color: #fff;
}
.edoc-primary-btn::after {
  content: "";
  position: absolute;
  width: 7.5em;
  aspect-ratio: 1;
  background: var(--edc-ternary);
  opacity: 50%;
  border-radius: 50%;
  transition: transform 700ms, background 600ms;
  left: 0;
  transform: translateX(-6.7em);
}
.edoc-primary-btn::before {
  content: "";
  position: absolute;
  width: 7.5em;
  aspect-ratio: 1;
  background: var(--edc-ternary);
  opacity: 50%;
  border-radius: 50%;
  transition: transform 700ms, background 600ms;
  right: 0;
  transform: translateX(6.7em);
}
.edoc-primary-btn:hover::after {
  transform: translateX(-5px);
  background: var(--edc-primary);
  width: 14em;
}
.edoc-primary-btn:hover::before {
  transform: translateX(5px);
  background: var(--edc-primary);
  width: 14em;
}
.edoc-primary-btn.active::after {
  background: var(--edc-primary);
}
.edoc-primary-btn.active::before {
  background: var(--edc-primary);
}
.edoc-primary-btn.primary-color {
  color: var(--edc-primary);
  background: var(--td-white);
  padding: 0 32px;
}
.edoc-primary-btn.primary-color:hover, .edoc-primary-btn.primary-color.active {
  color: #fff;
  z-index: 1;
}
.edoc-primary-btn.button-xl {
  height: 52px;
  font-size: 16px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-primary-btn.button-xl {
    height: 45px;
  }
}
@media (max-width: 767px) {
  .edoc-primary-btn.button-xl {
    height: 40px;
  }
}

/*----------------------------------------*/
/*  offcanvas
/*----------------------------------------*/
.edoc-offcanvas {
  position: fixed;
  z-index: 999;
  background: #fff;
  backdrop-filter: blur(5px);
  width: 250px;
  left: 0;
  top: 0;
  padding: 20px 20px;
  height: 100vh;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
  transition: 0.3s ease-in-out;
}
.edoc-offcanvas-open {
  visibility: visible;
  opacity: 1;
  transform: translateX(0);
}
.edoc-offcanvas-wrapper {
  position: relative;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-close {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul {
  list-style-type: none;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li {
  margin-bottom: 15px;
}
@media (max-width: 767px) {
  .edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li {
    margin-bottom: 8px;
  }
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li:last-child {
  margin-bottom: 0;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li > a {
  color: var(--edc-heading);
  font-size: 12px;
  font-weight: 500;
  line-height: 2;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li > a:hover {
  color: var(--edc-primary);
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li > a.active {
  position: relative;
  transition: all 0.3s ease-in-out;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li > a.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 1px;
  background-color: var(--td-heading);
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li > a .down-icon {
  color: var(--td-heading);
  margin-inline-end: 5px;
  display: flex;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li ul {
  list-style-type: none;
  margin-top: 8px;
  display: none;
}
.edoc-offcanvas-wrapper .edoc-offcanvas-navbars > ul > li ul li a {
  font-size: 14px;
  color: var(--td-text-primary);
}
.edoc-offcanvas-wrapper .header-top-content {
  margin-top: 20px;
}
.edoc-offcanvas-wrapper .header-top-content .header-top-right-card {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.edoc-offcanvas-wrapper .header-top-content .header-top-right-card:last-child {
  margin-bottom: 0;
}
.edoc-offcanvas-wrapper .header-top-content .header-top-right-card .icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 5px;
  background-color: var(--edc-primary);
}
.edoc-offcanvas-wrapper .header-top-content .header-top-right-card .icon .header-top-icon {
  color: var(--td-white);
  font-size: 18px;
}
.edoc-offcanvas-wrapper .header-top-content .header-top-right-card .text h6 {
  font-size: 14px;
  font-weight: 500;
  color: var(--edc-heading);
}
.edoc-offcanvas-wrapper .header-top-content .header-top-right-card .text p {
  font-size: 12px;
  font-weight: 400;
  color: var(--edc-text-primary);
}
.edoc-offcanvas-wrapper .appointment-btn {
  margin-top: 26px;
}
.edoc-offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.edoc-offcanvas-header .edoc-offcanvas-logo {
  display: inline-block;
  height: 24px;
}
.edoc-offcanvas-header .edoc-offcanvas-logo img {
  width: 100%;
  height: 100%;
}
.edoc-offcanvas-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 998;
  width: 100%;
  height: 100%;
  visibility: hidden;
  opacity: 0;
  transition: 0.45s ease-in-out;
  background: rgba(24, 24, 24, 0.4);
}
.edoc-offcanvas-overlay-open {
  visibility: visible;
  opacity: 0.7;
}

.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}

.edoc-offcanvas-close .edoc-offcanvas-close-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(1px);
}
.edoc-offcanvas-close .edoc-offcanvas-close-toggle .cancel-icon {
  font-size: 22px;
  color: var(--edc-heading);
}

/*----------------------------------------*/
/*  Headings
/*----------------------------------------*/
.edoc-common-title {
  background: linear-gradient(90deg, rgba(7, 187, 168, 0.2509803922) 0%, rgba(7, 187, 168, 0) 70%);
  border-left: 2px solid var(--edc-primary);
  display: inline-block;
  padding-left: 10px;
  margin-bottom: 10px;
}
.edoc-common-title p {
  color: var(--edc-primary);
  font-size: 20px;
  font-weight: 400;
  line-height: normal;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-common-title p {
    font-size: 18px;
    font-weight: 400;
  }
}
@media (max-width: 767px) {
  .edoc-common-title p {
    font-size: 16px;
    font-weight: 400;
  }
}
.edoc-common-title-2 {
  margin-bottom: 16px;
}

.edoc-website-title {
  color: var(--edc-heading);
  font-size: 80px;
  font-weight: 700;
  line-height: 1.125;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .edoc-website-title {
    font-size: 68px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-website-title {
    font-size: 60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-website-title {
    font-size: 52px;
  }
}
@media (max-width: 767px) {
  .edoc-website-title {
    font-size: 32px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-website-title {
    font-size: 42px;
  }
}

.edoc-website-subtitle {
  color: var(--edc-text-primary);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
}
@media (max-width: 767px) {
  .edoc-website-subtitle {
    font-size: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-website-subtitle {
    font-size: 18px;
  }
}

.edoc-common-section-title {
  width: 550px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.edoc-common-section-title h2 {
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
  color: var(--edc-heading);
  text-align: center;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-common-section-title h2 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-common-section-title h2 {
    font-size: 32px;
  }
}
@media (max-width: 767px) {
  .edoc-common-section-title h2 {
    font-size: 28px;
  }
}
.edoc-common-section-title-2 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
@media (max-width: 767px) {
  .edoc-common-section-title-2 {
    flex-direction: column;
    justify-content: start;
    align-items: start;
  }
}
@media (max-width: 767px) {
  .edoc-common-section-title-2 .right {
    margin-top: 16px;
  }
}
.edoc-common-section-title-white h2 {
  color: var(--td-white);
}
.edoc-common-section-title-sp h2 {
  font-size: 30px;
  font-weight: 600;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-common-section-title-sp h2 {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-common-section-title-sp h2 {
    font-size: 25px;
  }
}
@media (max-width: 767px) {
  .edoc-common-section-title-sp h2 {
    font-size: 20px;
  }
}
.edoc-common-section-title-3 .left h2 {
  width: 80%;
  text-align: left;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .edoc-common-section-title-3 .left h2 {
    margin-bottom: 20px;
  }
}
.edoc-common-section-title-4 .left h2 {
  text-align: left;
  margin-bottom: 16px;
}
.edoc-common-section-title-4 .left p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .edoc-common-section-title-4 .left p {
    margin-bottom: 20px;
  }
}

.common-page-top {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 52px 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .common-page-top {
    padding: 45px 0;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-page-top {
    padding: 40px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .common-page-top {
    padding: 35px 0;
  }
}
@media (max-width: 767px) {
  .common-page-top {
    padding: 25px 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-page-top {
    padding: 30px 0;
  }
}
.common-page-top .common-page-top-content h2 {
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
  color: var(--edc-heading);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-page-top .common-page-top-content h2 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-page-top .common-page-top-content h2 {
    font-size: 32px;
  }
}
@media (max-width: 767px) {
  .common-page-top .common-page-top-content h2 {
    font-size: 28px;
    margin-bottom: 10px;
  }
}
.common-page-top .common-page-top-content .breadcrumb-content ul {
  list-style-type: none;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
.common-page-top .common-page-top-content .breadcrumb-content ul li .icon {
  display: flex;
  align-items: center;
}
.common-page-top .common-page-top-content .breadcrumb-content ul li .icon .double-arrow-right {
  font-size: 20px;
  color: var(--edc-text-primary);
}
.common-page-top .common-page-top-content .breadcrumb-content ul li a {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
.common-page-top .common-page-top-content .breadcrumb-content ul li a.active {
  color: var(--edc-primary);
}

.sec-title-common {
  position: relative;
  margin-bottom: 20px;
}
.sec-title-common::after {
  position: absolute;
  content: "";
  width: 40px;
  height: 3px;
  background: #07BBA8;
  bottom: 0;
  left: 0;
}
.sec-title-common h5 {
  color: #093E63;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  padding-bottom: 8px;
}

.special-title {
  position: relative;
  margin-bottom: 16px;
}
.special-title::after {
  position: absolute;
  content: url("../images/edoccare/about-us/point.png");
  width: 46px;
  height: 16px;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
.special-title h3 {
  color: #093E63;
  font-size: 20px;
  font-weight: 600;
  line-height: normal;
  margin-left: 55px;
}

/*----------------------------------------*/
/*  card
/*----------------------------------------*/
.edoc-choose-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
  border-radius: 16px;
  background: var(--td-white);
  border: 1px solid rgba(9, 62, 99, 0.1);
  height: 100%;
}
.edoc-choose-card .icon {
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .edoc-choose-card .icon {
    width: 35px;
    height: 35px;
    margin-bottom: 16px;
  }
}
.edoc-choose-card .icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.edoc-choose-card .text h6 {
  color: var(--edc-heading);
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 8px;
}
.edoc-choose-card .text p {
  color: var(--edc-text-primary);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.4285714286;
}

.edoc-service-title-box {
  padding: 45px;
  border-radius: 32px;
  border: 1px solid var(--edc-primary);
  position: relative;
  overflow: hidden;
  height: 100%;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-service-title-box {
    padding: 30px;
  }
}
@media (max-width: 767px) {
  .edoc-service-title-box {
    padding: 24px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-service-title-box {
    padding: 30px;
  }
}
.edoc-service-title-box h2 {
  color: var(--edc-heading);
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .edoc-service-title-box h2 {
    font-size: 42px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-service-title-box h2 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-service-title-box h2 {
    font-size: 32px;
  }
}
@media (max-width: 767px) {
  .edoc-service-title-box h2 {
    font-size: 28px;
  }
}
.edoc-service-title-box p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.375;
}
.edoc-service-title-box .all-service-btn {
  margin-top: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-service-title-box .all-service-btn {
    margin-top: 24px;
  }
}
.edoc-service-title-box .element {
  position: absolute;
  bottom: -50px;
  right: -57px;
}

.edoc-service-card {
  border-radius: 32px;
  border: 1px solid var(--edc-primary);
  background: var(--td-white);
  position: relative;
  overflow: hidden;
  padding: 50px 16px;
  height: 100%;
  transition: all 0.3s ease-in-out;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-service-card {
    padding: 32px 22px;
  }
}
@media (max-width: 767px) {
  .edoc-service-card {
    padding: 26px 14px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-service-card {
    padding: 32px 22px;
  }
}
.edoc-service-card .content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.edoc-service-card .content .icon {
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .edoc-service-card .content .icon {
    margin-bottom: 12px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-service-card .content .icon {
    margin-bottom: 16px;
  }
}
.edoc-service-card .content .icon svg {
  width: 64px;
  height: 64px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-service-card .content .icon svg {
    width: 55px;
    height: 55px;
  }
}
@media (max-width: 767px) {
  .edoc-service-card .content .icon svg {
    width: 50px;
    height: 50px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-service-card .content .icon svg {
    width: 55px;
    height: 55px;
  }
}
.edoc-service-card .content h3 {
  color: var(--edc-heading);
  font-size: 30px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-service-card .content h3 {
    font-size: 26px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-service-card .content h3 {
    font-size: 24px;
  }
}
@media (max-width: 767px) {
  .edoc-service-card .content h3 {
    margin-bottom: 12px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-service-card .content h3 {
    margin-bottom: 16px;
  }
}
.edoc-service-card .content p {
  color: var(--edc-text-primary);
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4285714286;
}
.edoc-service-card .content .learn-more-btn {
  margin-top: 40px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-service-card .content .learn-more-btn {
    margin-top: 18px;
  }
}
.edoc-service-card .element {
  position: absolute;
  bottom: 0px;
  right: 0px;
}
.edoc-service-card:hover, .edoc-service-card.active {
  background-color: var(--edc-primary);
}
.edoc-service-card:hover .content .icon svg path, .edoc-service-card.active .content .icon svg path {
  fill: var(--td-white);
}
.edoc-service-card:hover .content h3, .edoc-service-card.active .content h3 {
  color: var(--td-white);
}
.edoc-service-card:hover .content p, .edoc-service-card.active .content p {
  color: var(--td-white);
}
.edoc-service-card:hover .content .learn-more-btn a, .edoc-service-card.active .content .learn-more-btn a {
  color: var(--td-white);
}
.edoc-service-card:hover .element svg g circle, .edoc-service-card.active .element svg g circle {
  fill: var(--td-white);
  stroke: var(--td-white);
}
.edoc-service-card.active:hover {
  background-color: var(--td-white);
}
.edoc-service-card.active:hover .content .icon svg path {
  fill: var(--edc-heading);
}
.edoc-service-card.active:hover .content h3 {
  color: var(--edc-heading);
}
.edoc-service-card.active:hover .content p {
  color: var(--edc-text-primary);
}
.edoc-service-card.active:hover .content .learn-more-btn a {
  color: var(--edc-heading);
}

.edoc-doctor-card {
  display: block;
  border-radius: 16px;
  background: var(--edc-primary);
  padding-top: 30px;
  position: relative;
  overflow: hidden;
}
.edoc-doctor-card .doctor-image {
  display: flex;
  justify-content: center;
  height: 310px;
}
@media (max-width: 767px) {
  .edoc-doctor-card .doctor-image {
    height: 220px;
  }
}
.edoc-doctor-card .doctor-image img {
  height: 100%;
}
.edoc-doctor-card .shape {
  position: absolute;
  top: 0;
  right: 0;
}
.edoc-doctor-card .content {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  transform: translateY(100%);
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
.edoc-doctor-card .content .box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 20px;
  padding-top: 100px;
  width: 100%;
  position: relative;
  z-index: 5;
  padding-left: 10px;
  padding-right: 10px;
}
.edoc-doctor-card .content .box .text {
  position: relative;
  z-index: 5;
}
.edoc-doctor-card .content .box .text h4 {
  color: var(--td-white);
  font-size: 26px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0.2px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-doctor-card .content .box .text h4 {
    font-size: 24px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-doctor-card .content .box .text h4 {
    font-size: 22px;
  }
}
@media (max-width: 767px) {
  .edoc-doctor-card .content .box .text h4 {
    font-size: 20px;
  }
}
.edoc-doctor-card .content .box .text p {
  color: var(--td-white);
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.2px;
  margin-bottom: 10px;
}
.edoc-doctor-card .content .box .view-profile {
  position: relative;
  z-index: 5;
}
.edoc-doctor-card .content .box::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(7, 187, 168, 0) 0%, rgba(9, 62, 99, 0.77) 73.56%, #093E63 100%);
  z-index: 0;
  border-radius: 16px;
}
.edoc-doctor-card:hover .content {
  transform: translateY(0);
  opacity: 1;
}

.edc-our-blog-card {
  padding: 16px;
  border-radius: 24px;
  border: 1px solid rgba(107, 139, 161, 0.1);
}
.edc-our-blog-card .img-box {
  width: 100%;
  height: 222px;
  display: block;
  border-radius: 8px;
}
.edc-our-blog-card .img-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}
.edc-our-blog-card .content .post-date {
  display: flex;
  align-items: center;
  gap: 50px;
  margin-bottom: 10px;
}
.edc-our-blog-card .content .post-date .post,
.edc-our-blog-card .content .post-date .date {
  display: flex;
  align-items: center;
  gap: 7px;
}
.edc-our-blog-card .content .post-date .post .icon,
.edc-our-blog-card .content .post-date .date .icon {
  display: flex;
}
.edc-our-blog-card .content .post-date .post .icon .post-icon,
.edc-our-blog-card .content .post-date .date .icon .post-icon {
  color: var(--edc-text-primary);
  font-size: 16px;
}
.edc-our-blog-card .content .post-date .post p,
.edc-our-blog-card .content .post-date .date p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.edc-our-blog-card .content .title span {
  display: block;
  color: var(--edc-heading);
  font-size: 20px;
  font-weight: 600;
  line-height: 1.5;
  margin-bottom: 10px;
  transition: all 0.3s ease-in-out;
}
.edc-our-blog-card .content .title span:hover {
  color: var(--edc-primary);
}
.edc-our-blog-card .content p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
}
.edc-our-blog-card .content .read-more-btn {
  margin-top: 22px;
}

.edoc-service-points-card {
  position: relative;
  padding-left: 24px;
  height: 100%;
}
.edoc-service-points-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: rgba(9, 62, 99, 0.06);
  border-radius: 0 50px 50px 0;
}
.edoc-service-points-card .icon {
  width: 50px;
  height: 50px;
  margin-bottom: 16px;
}
.edoc-service-points-card .icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.edoc-service-points-card .text h6 {
  color: var(--edc-heading);
  font-size: 16px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 8px;
}
.edoc-service-points-card .text p {
  color: var(--edc-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714;
}

/*----------------------------------------*/
/*  Progress bar
/*----------------------------------------*/
.edc-progressbar-box .edc-progress-box .p-title {
  color: var(--td-white);
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  display: block;
  margin-bottom: 5px;
}
.edc-progressbar-box .edc-progress-box .edc-progress {
  height: 10px;
  background: #EEEEEE;
  border-radius: 20px;
  overflow: visible;
  margin-bottom: 50px;
  position: relative;
}
.edc-progressbar-box .edc-progress-box .edc-progress .edc-progressbar {
  position: relative;
  border-radius: 20px;
  animation: animate-positive 2s;
  height: 100%;
}
.edc-progressbar-box .edc-progress-box .edc-progress .edc-progressvalue {
  position: absolute;
  bottom: -40px;
  right: 0;
  transform: translateX(47%);
  padding: 5px 14px;
  border-radius: 4px;
  background: #FFF;
  color: var(--edc-heading);
  text-align: center;
  z-index: 10;
  min-width: 64px;
  color: var(--edc-heading);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;
}
.edc-progressbar-box .edc-progress-box .edc-progress .edc-progressvalue:after {
  content: "";
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%) rotate(180deg);
  width: 16px;
  height: 16px;
  background: #FFF;
  clip-path: polygon(0% 0%, 100% 0%, 50% 100%);
}
.edc-progressbar-box .edc-progress-box-2 .p-title {
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
}
.edc-progressbar-box .edc-progress-box-2 .edc-progress .edc-progressvalue {
  background: var(--edc-heading);
  color: var(--td-white);
}
.edc-progressbar-box .edc-progress-box-2 .edc-progress .edc-progressvalue::after {
  background: var(--edc-heading);
}
@keyframes animate-positive {
  0% {
    width: 0%;
  }
}

/*----------------------------------------*/
/*  forms
/*----------------------------------------*/
input[type=text],
input[type=search],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
textarea {
  outline: none;
  height: 52px;
  width: 100%;
  padding: 0 16px;
  border-radius: 8px;
  border: 1px solid rgba(34, 34, 34, 0.1);
  color: rgba(8, 8, 8, 0.6);
  background: transparent;
  color: #093E63;
  font-size: 13px;
  font-weight: 400;
  line-height: normal;
}
input[type=text]::placeholder,
input[type=search]::placeholder,
input[type=email]::placeholder,
input[type=tel]::placeholder,
input[type=number]::placeholder,
input[type=password]::placeholder,
textarea::placeholder {
  color: rgba(9, 62, 99, 0.6);
}
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
textarea:focus {
  border-color: var(--edc-primary);
  box-shadow: unset;
  opacity: 1;
  border: 1px solid var(--edc-primary);
}
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
textarea:focus {
  border-color: var(--edc-primary);
  color: var(--ed-heading);
  background: transparent;
}
input[type=text]:focus::placeholder,
input[type=search]:focus::placeholder,
input[type=email]:focus::placeholder,
input[type=tel]:focus::placeholder,
input[type=number]:focus::placeholder,
input[type=password]:focus::placeholder,
textarea:focus::placeholder {
  opacity: 0;
  transition: opacity 0.3s ease;
}
input[type=text].input-exceptional,
input[type=search].input-exceptional,
input[type=email].input-exceptional,
input[type=tel].input-exceptional,
input[type=number].input-exceptional,
input[type=password].input-exceptional,
textarea.input-exceptional {
  background: #f4f4f4;
}
input[type=text].input-exceptional-2,
input[type=search].input-exceptional-2,
input[type=email].input-exceptional-2,
input[type=tel].input-exceptional-2,
input[type=number].input-exceptional-2,
input[type=password].input-exceptional-2,
textarea.input-exceptional-2 {
  height: 35px;
  background-color: var(--td-white);
}
input[type=text].input-design-2,
input[type=search].input-design-2,
input[type=email].input-design-2,
input[type=tel].input-design-2,
input[type=number].input-design-2,
input[type=password].input-design-2,
textarea.input-design-2 {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 0;
  color: var(--td-white);
}
input[type=text].input-design-2:focus,
input[type=search].input-design-2:focus,
input[type=email].input-design-2:focus,
input[type=tel].input-design-2:focus,
input[type=number].input-design-2:focus,
input[type=password].input-design-2:focus,
textarea.input-design-2:focus {
  border-color: var(--td-white);
  box-shadow: unset;
  opacity: 1;
  background-color: rgba(8, 8, 8, 0.2);
}
input[type=text].input-design-pxNone,
input[type=search].input-design-pxNone,
input[type=email].input-design-pxNone,
input[type=tel].input-design-pxNone,
input[type=number].input-design-pxNone,
input[type=password].input-design-pxNone,
textarea.input-design-pxNone {
  padding: 0 0;
}
input[type=text].input-design-pxNone:focus,
input[type=search].input-design-pxNone:focus,
input[type=email].input-design-pxNone:focus,
input[type=tel].input-design-pxNone:focus,
input[type=number].input-design-pxNone:focus,
input[type=password].input-design-pxNone:focus,
textarea.input-design-pxNone:focus {
  background-color: rgba(8, 8, 8, 0);
}

textarea {
  padding: 14px 16px;
  height: 135px !important;
  color: var(--edc-heading);
}
textarea::placeholder {
  color: rgba(9, 62, 99, 0.6);
}
textarea:focus {
  border-color: var(--edc-primary);
}

input[type=checkbox],
input[type=radio] {
  opacity: 0;
  position: absolute;
}
input[type=checkbox] ~ label,
input[type=radio] ~ label {
  position: relative;
  font-size: 14px;
  line-height: line-height(25, 14);
  color: var(--td-text-primary);
  font-weight: 400;
  padding-inline-start: 20px;
  cursor: pointer;
  margin-bottom: 0;
}
input[type=checkbox] ~ label a,
input[type=radio] ~ label a {
  color: var(--td-primary);
  transition: all 0.3s ease-in-out;
}
input[type=checkbox] ~ label a:hover,
input[type=radio] ~ label a:hover {
  color: var(--td-heading);
}
input[type=checkbox] ~ label::before,
input[type=radio] ~ label::before {
  content: " ";
  position: absolute;
  top: 3px;
  inset-inline-start: 0;
  width: 14px;
  height: 14px;
  background-color: var(--td-white);
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  transition: all 0.3s;
}
input[type=checkbox] ~ label::after,
input[type=radio] ~ label::after {
  content: " ";
  position: absolute;
  top: 7px;
  inset-inline-start: 2px;
  width: 10px;
  height: 5px;
  background-color: transparent;
  border-bottom: 1px solid var(--td-white);
  border-inset-inline-start: 1px solid var(--td-white);
  border-radius: 2px;
  opacity: 0;
  transition: all 0.3s;
}
input[type=checkbox]:checked ~ label::before,
input[type=radio]:checked ~ label::before {
  background-color: var(--td-white);
  border-color: #dbdbdb;
}
input[type=checkbox]:checked ~ label::after,
input[type=radio]:checked ~ label::after {
  opacity: 1;
}

input[type=radio] ~ label::before {
  border-radius: 50%;
}
input[type=radio] ~ label::after {
  width: 6.1px;
  height: 7px;
  inset-inline-start: 4px;
  background: var(--td-primary);
  border-radius: 50%;
}

.animate-custom .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}
.animate-custom .cbx span {
  display: inline-block;
  vertical-align: middle;
}
.animate-custom .cbx span a {
  color: var(--td-primary);
}
.animate-custom .cbx span a:hover {
  color: #000000;
}
.animate-custom .cbx span:first-child {
  position: relative;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid #b9b8c3;
  transition: all 0.2s ease;
}
.animate-custom .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 4px;
  inset-inline-start: 2px;
  fill: none;
  stroke: var(--td-white);
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.animate-custom .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: var(--td-primary);
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}
.animate-custom .cbx span:last-child {
  margin-inset-inline-start: 6px;
  color: var(--td-text-primary);
  font-weight: 500;
  font-size: 14px;
}
.animate-custom .cbx span:last-child:after {
  content: "";
  position: absolute;
  top: 8px;
  inset-inline-start: 0;
  height: 1px;
  width: 100%;
  background: #b9b8c3;
  transform-origin: 0 0;
  transform: scaleX(0);
}
.animate-custom .cbx:hover span:first-child {
  border-color: var(--td-primary);
}
.animate-custom .inp-cbx:checked + .cbx span:first-child {
  border-color: var(--td-primary);
  background: var(--td-primary);
  animation: check-15 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
  transition: all 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:last-child {
  transition: all 0.3s ease;
}
.animate-custom input[type=checkbox] ~ label::after {
  display: none;
}
.animate-custom input[type=checkbox] ~ label {
  padding-inline-start: 0;
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}
.td-form-group .input-label {
  color: var(--edc-heading);
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
.td-form-group .input-label span {
  color: #EC0707;
}

.was-validated .td-form-group .input-field {
  position: relative;
}
.was-validated .td-form-group .input-field input {
  border-color: var(--td-danger);
  background: rgba(220, 29, 75, 0.1);
}
.was-validated .td-form-group .input-field input:focus {
  background: rgba(220, 29, 75, 0.1);
}

.edc-form .td-form-group.has-right-icon .box-input {
  padding-inline-end: 50px;
}
.edc-form .td-form-group.has-right-icon .input-icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}
.edc-form .td-form-group.has-right-icon .input-icon i {
  font-size: 14px;
}
.edc-form .td-form-group.has-right-icon .input-icon.eyeicon {
  cursor: pointer;
  inset-inline-end: 20px !important;
  inset-inline-start: auto !important;
}
.edc-form .td-form-group.has-right-icon .input-icon.icon-selected svg * {
  stroke: rgba(8, 8, 8, 0.7);
  /* Change stroke color */
  fill: rgba(8, 8, 8, 0.7);
  /* Change stroke color */
  stroke-opacity: 1;
  /* Full opacity */
  transition: all 0.3s ease;
  /* Smooth animation */
}
.edc-form .td-form-group.selected_icon .input-icon {
  inset-inline-end: 33px;
  cursor: pointer;
}
.edc-form .td-form-group.has-left-icon .box-input {
  padding-inline-start: 45px;
}
.edc-form .td-form-group.has-left-icon .input-icon {
  position: absolute;
  inset-inline-start: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  width: max-content;
}
.edc-form .td-form-group.has-left-icon .input-icon.eyeicon {
  cursor: pointer;
}
.edc-form .td-form-group .input-field {
  position: relative;
}
.edc-form .td-form-group .input-field.date-of-birth {
  position: relative;
}
.edc-form .td-form-group .input-field.date-of-birth .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 15px;
}
.edc-form .td-form-group .input-field.has-right-icon {
  position: relative;
}
.edc-form .td-form-group .input-field.has-right-icon .form-control {
  color: #008080;
}
.edc-form .td-form-group .input-field.has-right-icon .icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #f8f9fa;
}
.edc-form .td-form-group .input-field.has-right-icon .icon .copy-icon {
  font-size: 14px;
  color: #6b7280;
}
.edc-form .td-form-group .input-field.has-right-icon .icon .copy-tooltip {
  position: absolute;
  top: -30px;
  inset-inline-end: 0;
  background-color: #000;
  color: #fff;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}
.edc-form .td-form-group .input-field.has-right-icon .icon.show-tooltip .copy-tooltip {
  opacity: 1;
}
.edc-form .td-form-group .input-field .edit-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 15px;
  height: 20px;
  display: flex;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: var(--td-card-bg-1);
  color: var(--td-white);
  font-size: 0.625rem;
  font-weight: 400;
  line-height: 1.6;
}
.edc-form .td-form-group .input-field.input-group {
  flex-wrap: nowrap;
}
.edc-form .td-form-group .input-field .input-group-text {
  color: var(--td-white);
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.edc-form .td-form-group .input-field.disabled input,
.edc-form .td-form-group .input-field.disabled textarea {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}
.edc-form .td-form-group .input-field.disabled input:focus,
.edc-form .td-form-group .input-field.disabled textarea:focus {
  border-color: rgba(255, 255, 255, 0.08);
}
.edc-form .td-form-group .input-field .text-content {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  position: absolute;
  top: 50%;
  inset-inline-end: 5px;
  transform: translateY(-50%);
  padding: 5px 8px 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
}
.edc-form .td-form-group .input-field input,
.edc-form .td-form-group .input-field textarea {
  font-size: 14px;
  letter-spacing: -0.03em;
}
.edc-form .td-form-group .input-field input::-webkit-input-placeholder,
.edc-form .td-form-group .input-field textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.edc-form .td-form-group .input-field input::-moz-placeholder,
.edc-form .td-form-group .input-field textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.edc-form .td-form-group .input-field input:-moz-placeholder,
.edc-form .td-form-group .input-field textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.edc-form .td-form-group .input-field input:-ms-input-placeholder,
.edc-form .td-form-group .input-field textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.edc-form .td-form-group .input-field input::placeholder,
.edc-form .td-form-group .input-field textarea::placeholder {
  /* MODERN BROWSER */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.edc-form .td-form-group .input-field textarea {
  padding: 12px 15px;
  height: 156px;
  resize: none;
  line-height: 1.5;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  color: var(--td-white);
  font-size: 14px;
  font-weight: 300;
}
.edc-form .td-form-group .input-field textarea::placeholder {
  color: #9DB2C1;
  font-size: 14px;
  font-weight: 300;
  line-height: 100%;
}
.edc-form .td-form-group .input-field textarea:focus {
  border: 1px solid #8ba7bb;
}
.edc-form .td-form-group .input-field.height-large textarea {
  height: 237px;
}
.edc-form .td-form-group .input-field .form-control {
  height: 52px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  color: var(--td-white);
  font-size: 14px;
  font-weight: 300;
  line-height: 100%;
}
.edc-form .td-form-group .input-field .form-control::placeholder {
  color: #9DB2C1;
  font-size: 14px;
  font-weight: 300;
  line-height: 100%;
}
.edc-form .td-form-group .input-field .form-control:focus {
  border: 1px solid #8ba7bb;
}
.edc-form .td-form-group .input-field-icon input {
  padding: 0 45px 0 15px;
}
[dir=rtl] .edc-form .td-form-group .input-field-icon input {
  padding: 0 15px 0 45px;
}
.edc-form .td-form-group .input-field-exceptional {
  margin-top: 8px;
}
.edc-form .td-form-group .input-field-phone {
  position: relative;
}
.edc-form .td-form-group .input-field-phone .form-control {
  padding: 0 15px 0 75px;
}
.edc-form .td-form-group .input-field-phone .country-code {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 15px;
  padding-inline-end: 10px;
  border-inline-end: 1px solid #cacaca;
}
.edc-form .td-form-group .input-description {
  font-size: 12px;
  margin-top: 7px;
}
.edc-form .td-form-group .input-label {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
  margin-bottom: 0.4em;
}
.edc-form .td-form-group .input-label span {
  padding-inline-start: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ec0707;
}
.edc-form .td-form-group .input-label-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.edc-form .td-form-group .input-label-inner > p {
  font-size: 12px;
}
.edc-form .td-form-group .input-select .nice-select {
  height: 44px;
  width: 100%;
  padding: 0 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.08);
}
.edc-form .td-form-group .input-select .nice-select .current {
  text-align: left;
  font-size: 14px;
  position: relative;
  color: var(--td-white);
}
.edc-form .td-form-group .input-select .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: #242424;
  border-radius: 12px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.08);
  border-width: 1px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.edc-form .td-form-group .input-select .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-inline-end: 1.5px solid var(--td-text-primary);
  font-size: 16px;
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border: 5px solid;
  border-top-color: rgba(0, 0, 0, 0);
  border-left-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  transition: all ease-in-out 0.2s;
  margin-top: -2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
}
.edc-form .td-form-group .input-select .nice-select .option {
  font-size: 14px;
  line-height: 38px;
  min-height: 38px;
  color: var(--td-white);
  border-radius: 10px;
  padding: 0 10px;
}
.edc-form .td-form-group .input-select .nice-select .option.selected {
  font-weight: 500;
}
.edc-form .td-form-group .input-select .nice-select .option:hover {
  background-color: #353535;
}
.edc-form .td-form-group .input-select .nice-select .option.selected.focus {
  background-color: #353535;
}
.edc-form .td-form-group .input-select .nice-select.open, .edc-form .td-form-group .input-select .nice-select:focus {
  background-color: #353535;
}
.edc-form .td-form-group.input-fill .input-label {
  font-weight: 700;
}
.edc-form .td-form-group.input-fill input,
.edc-form .td-form-group.input-fill select,
.edc-form .td-form-group.input-fill textarea {
  background-color: #fcfcfc;
  border: 1px solid rgba(8, 8, 8, 0.2);
}
.edc-form .td-form-group.input-fill input:focus,
.edc-form .td-form-group.input-fill select:focus,
.edc-form .td-form-group.input-fill textarea:focus {
  border-color: var(--td-primary);
}
.edc-form .td-form-group .form-select {
  height: 50px;
  border-radius: 8px;
  font-size: 14px;
}
.edc-form .td-form-group .form-select:focus {
  font-size: 14px;
}
.edc-form .td-form-group .otp-verification {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px 10px;
  flex-wrap: wrap;
  max-width: max-content;
  justify-content: center;
  margin: 0 auto;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .edc-form .td-form-group .otp-verification {
    gap: 10px 10px;
  }
}
.edc-form .td-form-group .otp-verification input {
  background: rgba(103, 107, 113, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 69.83px;
  height: 77px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .edc-form .td-form-group .otp-verification input {
    height: 55px;
    width: 50px;
  }
}

.feedback-invalid {
  font-size: 12px;
  margin-top: 3px;
  color: #dc1d4b;
  display: none;
}
.feedback-invalid.active {
  display: block;
}

.input-attention {
  font-size: 12px;
  color: var(--tdvar(--td-danger));
}
.input-attention.xs {
  font-size: 10px;
}

*::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

*::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

.common-select2-dropdown .select2-container {
  width: 100% !important;
}
.common-select2-dropdown .select2-container.select2-container--open .select2-selection--single {
  border-radius: 20px 20px 0 0;
}
.common-select2-dropdown .select2-container .select2-selection--single {
  height: 45px;
  border-radius: 40px;
}
.common-select2-dropdown .select2-container--default .select2-selection--single {
  border: 1px solid #cacaca;
  background: #fff;
}
.common-select2-dropdown .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: rgba(8, 8, 8, 0.6);
  line-height: 43px;
  font-size: 14px;
  padding-inline-end: 35px;
  padding-inline-start: 14px;
}
.common-select2-dropdown .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 40px;
  position: absolute;
  top: 1px;
  inset-inline-end: 10px;
  width: 20px;
}
.common-select2-dropdown .select2-dropdown {
  background-color: var(--td-bg);
  border: 1px solid var(--td-card-bg-2);
  border-radius: 4px;
}
.common-select2-dropdown .select2-results__option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.common-select2-dropdown .select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
  color: #fff;
  padding: 0 15px;
}
.common-select2-dropdown .select2-results__option {
  padding: 6px 15px;
  user-select: none;
  -webkit-user-select: none;
  font-size: 14px;
  color: #fff;
}

/*----------------------------------------*/
/*  nice select
/*----------------------------------------*/
.edc-custom-nice-select .nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 13px;
  font-weight: 400;
  height: 50px;
  line-height: 49px;
  color: var(--td-white);
  width: 100%;
  padding-left: 15px;
}
.edc-custom-nice-select .nice-select:after {
  border-bottom: 1px solid #9DB2C1;
  border-right: 1px solid #9DB2C1;
  right: 15px;
  height: 7px;
  width: 7px;
}
.edc-custom-nice-select .nice-select .list {
  background-color: #062A44;
  width: 100%;
  border-radius: 7px;
  color: #6B8BA1;
  font-size: 14px;
}
.edc-custom-nice-select .nice-select .option:hover,
.edc-custom-nice-select .nice-select .option.focus,
.edc-custom-nice-select .nice-select .option.selected.focus {
  background-color: #083555;
  font-weight: 400;
  color: #fff;
  font-size: 14px;
}
.edc-custom-nice-select-2 .nice-select {
  border: 1px solid rgba(34, 34, 34, 0.1);
  color: #093E63;
}
.edc-custom-nice-select-2 .nice-select.open {
  border: 1px solid var(--edc-primary);
}
.edc-custom-nice-select-2 .nice-select .list {
  background-color: #f0f0f0;
  width: 100%;
  border-radius: 7px;
  color: #093E63;
  font-size: 14px;
}
.edc-custom-nice-select-2 .nice-select .option:hover,
.edc-custom-nice-select-2 .nice-select .option.focus,
.edc-custom-nice-select-2 .nice-select .option.selected.focus {
  background-color: #dbdbdb;
  font-weight: 400;
  color: #093E63;
  font-size: 14px;
}

/*----------------------------------------*/
/*  testimonial
/*----------------------------------------*/
.edoc-testimonial {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.edoc-testimonial .edoc-testimonial-slider-full {
  display: flex;
  justify-content: center;
  align-items: center;
}
.edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-btn,
.edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-next {
  display: flex;
  width: 50px;
  height: 50px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 8px;
  background: var(--edc-primary);
  border: 1px solid var(--edc-primary);
  flex-shrink: 0;
}
@media (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-btn,
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-next {
    width: 30px;
    height: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-btn,
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-next {
    width: 40px;
    height: 40px;
  }
}
.edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-btn .arrow-icon,
.edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-next .arrow-icon {
  font-size: 30px;
  color: var(--td-white);
}
@media (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-btn .arrow-icon,
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-next .arrow-icon {
    font-size: 18px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-btn .arrow-icon,
  .edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-next .arrow-icon {
    font-size: 24px;
  }
}
.edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-btn.swiper-button-disabled,
.edoc-testimonial .edoc-testimonial-slider-full .edc-slider-prev-next.swiper-button-disabled {
  border: 1px solid var(--edc-primary);
  background: rgba(7, 187, 168, 0.1);
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box {
  width: 100%;
  max-width: 1064px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 50px;
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 50px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content {
    gap: 25px;
  }
}
@media (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content {
    gap: 5px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content {
    gap: 20px;
  }
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card {
  position: relative;
  margin-bottom: 40px;
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card {
  padding: 50px;
  border-radius: 10px;
  background: #073352;
  box-shadow: 0px 20px 60px 0px rgba(46, 33, 61, 0.08);
  position: relative;
  z-index: 5;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card {
    padding: 40px;
  }
}
@media (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card {
    padding: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card {
    padding: 30px;
  }
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content {
  position: relative;
  z-index: 5;
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .left {
  margin-right: 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .left {
    margin-right: 0px;
  }
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .left .img-box {
  width: 100%;
  height: 330px;
  border-radius: 16px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .left .img-box {
    height: 580px;
  }
}
@media (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .left .img-box {
    height: 24 0px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .left .img-box {
    height: 380px;
  }
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .left .img-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .right h3 {
  color: var(--td-white);
  font-size: 40px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -1.6px;
  text-transform: capitalize;
  margin-bottom: 16px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .right h3 {
    font-size: 34px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .right h3 {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .right h3 {
    font-size: 26px;
  }
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .content .right p {
  color: var(--td-white);
  font-size: 16px;
  font-style: normal;
  font-weight: 300;
  line-height: 1.625;
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .elements .elem-1 {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 4;
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card .edoc-testimonial-slider-card .elements .elem-2 {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 4;
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card::after {
  content: "";
  height: 100px;
  width: 90%;
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  border-radius: 10px;
  background: #245275;
  box-shadow: 0px 20px 60px 0px rgba(46, 33, 61, 0.08);
}
.edoc-testimonial .edoc-testimonial-slider-full .testimonial-slider-box .testimonial-slider-box-content .edoc-testimonial-card::before {
  content: "";
  height: 100px;
  width: 80%;
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  border-radius: 10px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.07);
  box-shadow: 0px 20px 60px 0px rgba(46, 33, 61, 0.08);
}

/*----------------------------------------*/
/*  pagination
/*----------------------------------------*/
.common-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-pagination ul {
  display: flex;
  align-items: center;
  gap: 10px;
  list-style-type: none;
}
.common-pagination ul li a {
  font-family: var(--edc-heading);
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  border-radius: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border: 1px solid #07BBA8;
  color: var(--edc-heading);
  text-align: center;
  font-size: 20px;
  font-weight: 400;
  line-height: normal;
}
.common-pagination ul li a:hover, .common-pagination ul li a.active {
  border: 1px solid var(--edc-primary);
  background: rgba(7, 187, 168, 0.1);
}
.common-pagination ul li a.navigation.disabled .arrow {
  opacity: 0.5;
  cursor: not-allowed;
}
.common-pagination ul li a.navigation:hover, .common-pagination ul li a.navigation.active {
  border-color: var(--edc-primary);
  background: var(--edc-primary);
}
.common-pagination ul li a.navigation:hover .arrow, .common-pagination ul li a.navigation.active .arrow {
  color: var(--td-white);
  opacity: 1;
}

/* Header css*/
/*----------------------------------------*/
/*  3.1 Header-1
/*----------------------------------------*/
.edc-header .header-top {
  background-color: #CEFFFA;
}
.edc-header .header-top .header-top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.edc-header .header-top .header-top-content .left .logo a {
  display: block;
  height: 32px;
}
.edc-header .header-top .header-top-content .left .logo a img {
  height: 100%;
  width: 100%;
}
.edc-header .header-top .header-top-content .right {
  display: flex;
  align-items: center;
}
.edc-header .header-top .header-top-content .right .header-top-right-card {
  display: flex;
  align-items: center;
  border-right: 2px solid #A8FFF6;
  padding: 20px 60px;
  gap: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .edc-header .header-top .header-top-content .right .header-top-right-card {
    padding: 16px 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edc-header .header-top .header-top-content .right .header-top-right-card {
    padding: 14px 36px;
  }
}
.edc-header .header-top .header-top-content .right .header-top-right-card:first-child {
  padding-left: 0;
}
.edc-header .header-top .header-top-content .right .header-top-right-card .icon {
  display: flex;
}
.edc-header .header-top .header-top-content .right .header-top-right-card .icon .header-top-icon {
  font-size: 24px;
  color: var(--edc-primary);
}
.edc-header .header-top .header-top-content .right .header-top-right-card .text h6 {
  font-size: 14px;
  font-weight: 500;
  color: var(--edc-heading);
  margin-bottom: 4px;
}
.edc-header .header-top .header-top-content .right .header-top-right-card .text p {
  font-size: 14px;
  font-weight: 400;
  color: var(--edc-text-primary);
}
.edc-header .main-header {
  background-color: var(--edc-heading);
  padding: 18px 0;
}
.edc-header .main-header .main-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.edc-header .main-header .main-header-content .left .logo {
  height: 32px;
}
@media (max-width: 767px) {
  .edc-header .main-header .main-header-content .left .logo {
    height: 26px;
  }
}
.edc-header .main-header .main-header-content .left .logo img {
  height: 100%;
  width: 100%;
}
.edc-header .main-header .main-header-content .left .menu ul {
  list-style-type: none;
  display: flex;
  gap: 30px;
}
.edc-header .main-header .main-header-content .left .menu ul li a {
  color: var(--td-white);
  font-size: 16px;
  font-weight: 300;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.edc-header .main-header .main-header-content .left .menu ul li a:hover, .edc-header .main-header .main-header-content .left .menu ul li a.active {
  color: var(--edc-primary);
}
.edc-header .main-header .main-header-content .right {
  display: flex;
  align-items: center;
  gap: 10px;
}
@media (max-width: 767px) {
  .edc-header .main-header .main-header-content .right {
    gap: 6px;
  }
}
@media (max-width: 767px) {
  .edc-header .main-header .main-header-content .right .action-btn {
    display: none;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edc-header .main-header .main-header-content .right .action-btn {
    display: block;
  }
}
.edc-header .main-header .main-header-content .right .toggle-btn .edoc-toggle-btn {
  display: flex;
}
.edc-header .main-header .main-header-content .right .toggle-btn .edoc-toggle-btn .menu-icon {
  font-size: 30px;
  color: var(--td-white);
}
@media (max-width: 767px) {
  .edc-header .main-header .main-header-content .right .toggle-btn .edoc-toggle-btn .menu-icon {
    font-size: 24px;
  }
}

/*----------------------------------------*/
/*  3.2 Header-2
/*----------------------------------------*/
/*----------------------------------------*/
/*  Hero
/*----------------------------------------*/
.edoc-hero {
  background: linear-gradient(90deg, rgba(7, 187, 168, 0.1) -21.65%, rgba(7, 187, 168, 0) 59.96%);
  margin-top: 24px;
  position: relative;
}
.edoc-hero .hero-bg-element {
  clip-path: polygon(0.426% 17.006%, 0.426% 17.006%, 0.107% 14.501%, 0.064% 12.031%, 0.275% 9.65%, 0.721% 7.412%, 1.378% 5.371%, 2.227% 3.581%, 3.246% 2.095%, 4.414% 0.967%, 5.71% 0.251%, 7.113% 0%, 100% 0%, 100% 100%, 20.86% 100%, 20.86% 100%, 19.964% 99.896%, 19.095% 99.592%, 18.264% 99.098%, 17.478% 98.425%, 16.745% 97.584%, 16.074% 96.586%, 15.473% 95.443%, 14.951% 94.163%, 14.515% 92.76%, 14.174% 91.243%, 0.426% 17.006%);
  background: var(--edc-primary);
  width: 943px;
  height: 503px;
  position: absolute;
  top: 165px;
  right: 0;
  z-index: 2;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .hero-bg-element {
    width: 820px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .hero-bg-element {
    width: 665px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .hero-bg-element {
    width: 540px;
    height: 370px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .hero-bg-element {
    display: none;
  }
}
.edoc-hero .bg-shape-element {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.edoc-hero .rectangle-shape {
  position: absolute;
  bottom: 0;
  right: 68px;
  z-index: 3;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .rectangle-shape {
    display: none;
  }
}
.edoc-hero .elem-1 {
  position: absolute;
  top: 160px;
  right: 22px;
  z-index: 3;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .elem-1 {
    top: 167px;
    right: 290px;
  }
}
.edoc-hero .elem-2 {
  position: absolute;
  top: 192px;
  right: 700px;
  z-index: 3;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .elem-2 {
    top: 175px;
    right: 580px;
  }
}
.edoc-hero .elem-3 {
  position: absolute;
  top: 30px;
  right: 800px;
  z-index: 3;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .elem-3 {
    top: 20px;
    right: 500px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .elem-3 {
    display: none;
  }
}
.edoc-hero .elem-4 {
  position: absolute;
  top: 65px;
  right: 280px;
  z-index: 3;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .elem-4 {
    right: 420px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-hero .elem-4 {
    right: 320px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .elem-4 {
    display: none;
  }
}
.edoc-hero .stats-card {
  padding: 16px;
  border-radius: 5px;
  background: var(--td-white);
  box-shadow: 0px 12px 56px 0px rgba(6, 28, 61, 0.12);
  position: absolute;
  left: 51%;
  top: 272px;
  z-index: 6;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .stats-card {
    left: 42%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-hero .stats-card {
    left: 47%;
    top: 290px;
    padding: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-hero .stats-card {
    left: 15%;
    top: 320px;
  }
}
@media (max-width: 767px) {
  .edoc-hero .stats-card {
    left: 8%;
    top: 320px;
    display: none;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-hero .stats-card {
    left: 8%;
    top: 320px;
    display: block;
  }
}
.edoc-hero .stats-card .icon {
  display: flex;
  width: 50px;
  height: 50px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  background: var(--edc-primary);
  border-radius: 8px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .stats-card .icon {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }
}
.edoc-hero .stats-card .icon img {
  width: 30px;
  height: 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .stats-card .icon img {
    width: 20px;
    height: 20px;
  }
}
.edoc-hero .stats-card h5 {
  color: var(--edc-heading);
  font-size: 24px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 4px;
}
.edoc-hero .stats-card h6 {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 300;
  line-height: normal;
  margin-bottom: 10px;
}
.edoc-hero .stats-card .star-box {
  display: flex;
  align-items: center;
  gap: 6px;
}
.edoc-hero .stats-card .star-box p {
  color: var(--edc-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
.edoc-hero .doctors-card {
  padding: 20px;
  border-radius: 12px;
  background: var(--td-white);
  position: absolute;
  bottom: 36px;
  right: 12%;
  z-index: 6;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .doctors-card {
    padding: 10px;
  }
}
.edoc-hero .doctors-card h5 {
  color: var(--edc-heading);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4285714286;
  letter-spacing: -0.168px;
  margin-bottom: 12px;
}
.edoc-hero .doctors-card .doctors {
  display: flex;
  align-items: center;
}
.edoc-hero .doctors-card .doctors .doctor {
  width: 39px;
  height: 39px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: -13px;
  border: 1px solid var(--td-white);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .doctors-card .doctors .doctor {
    width: 30px;
    height: 30px;
  }
}
.edoc-hero .doctors-card .doctors .doctor:last-child {
  margin-right: 0;
}
.edoc-hero .doctors-card .doctors .doctor .counter {
  width: 39px;
  height: 39px;
  background-color: var(--edc-primary);
  color: var(--td-white);
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  line-height: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .doctors-card .doctors .doctor .counter {
    width: 30px;
    height: 30px;
    font-size: 12px;
    font-weight: 500;
  }
}
.edoc-hero .doctors-card .doctors .doctor img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.edoc-hero .edoc-hero-content {
  position: relative;
  z-index: 5;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-hero .edoc-hero-content .left {
    margin-top: 30px;
  }
}
@media (max-width: 767px) {
  .edoc-hero .edoc-hero-content .left {
    margin-top: 5px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-hero .edoc-hero-content .left {
    margin-top: 30px;
  }
}
.edoc-hero .edoc-hero-content .left .edoc-website-title {
  margin-bottom: 16px;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-hero .edoc-hero-content .left .edoc-website-title {
    width: 90%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .left .edoc-website-title {
    width: 100%;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .left .edoc-website-subtitle {
    width: 70%;
  }
}
@media (max-width: 767px) {
  .edoc-hero .edoc-hero-content .left .edoc-website-subtitle {
    width: 90%;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-hero .edoc-hero-content .left .edoc-website-subtitle {
    width: 70%;
  }
}
.edoc-hero .edoc-hero-content .left .edoc-action-btn {
  margin-top: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .left .edoc-action-btn {
    margin-top: 25px;
  }
}
.edoc-hero .edoc-hero-content .right {
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right {
    margin-top: -40px;
  }
}
@media (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right {
    margin-top: -20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right {
    margin-top: -40px;
  }
}
.edoc-hero .edoc-hero-content .right .hero-img {
  height: 624px;
  margin-top: 20px;
  display: flex;
  justify-content: end;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-img {
    height: 491px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-img {
    z-index: 5;
    position: relative;
    justify-content: center;
    height: 424px;
  }
}
@media (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-img {
    height: 300px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-img {
    height: 424px;
  }
}
.edoc-hero .edoc-hero-content .right .hero-img img {
  height: 100%;
}
.edoc-hero .edoc-hero-content .right .hero-bg-element-2 {
  background: var(--edc-primary);
  width: 100%;
  height: 503px;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  bottom: 0;
  left: 0;
  z-index: 2;
  transform: rotate(180deg);
  border-radius: 0 0 20px 20px;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-bg-element-2 {
    width: 820px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-bg-element-2 {
    width: 665px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-bg-element-2 {
    width: 100%;
    height: 270px;
  }
}
@media (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-bg-element-2 {
    height: 170px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-hero .edoc-hero-content .right .hero-bg-element-2 {
    height: 270px;
  }
}

/*----------------------------------------*/
/*  About
/*----------------------------------------*/
.edoc-about-us {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 187px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-about-us {
    padding-top: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-us {
    padding-top: 64px;
  }
}
@media (max-width: 767px) {
  .edoc-about-us {
    padding-top: 36px;
  }
}
@media (max-width: 767px) {
  .edoc-about-us {
    padding-top: 50px;
  }
}
.edoc-about-us .edoc-about-us-content .left {
  position: relative;
}
.edoc-about-us .edoc-about-us-content .left .main-img {
  height: 542px;
  position: relative;
  z-index: 5;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .left .main-img {
    height: 460px;
    transform: translateY(47px);
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .left .main-img {
    height: 380px;
    transform: translateY(0px);
    display: flex;
    justify-content: center;
  }
}
@media (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .left .main-img {
    height: 290px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .left .main-img {
    height: 380px;
  }
}
.edoc-about-us .edoc-about-us-content .left .main-img img {
  height: 100%;
}
.edoc-about-us .edoc-about-us-content .left .oval-element {
  width: 354px;
  height: 333px;
  flex-shrink: 0;
  background-color: var(--edc-primary);
  position: absolute;
  top: -100px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 50%;
  z-index: 4;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .left .oval-element {
    top: -15px;
  }
}
@media (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .left .oval-element {
    width: 250px;
    height: 225px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .left .oval-element {
    width: 354px;
    height: 333px;
  }
}
.edoc-about-us .edoc-about-us-content .right {
  padding-left: 145px;
  padding-bottom: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .right {
    padding-left: 45px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-about-us .edoc-about-us-content .right {
    padding-left: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .right {
    padding-left: 0px;
  }
}
.edoc-about-us .edoc-about-us-content .right .text {
  width: 85%;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .right .text {
    width: 100%;
  }
}
.edoc-about-us .edoc-about-us-content .right .text h2 {
  color: var(--td-white);
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .edoc-about-us .edoc-about-us-content .right .text h2 {
    font-size: 42px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-about-us .edoc-about-us-content .right .text h2 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-about-us .edoc-about-us-content .right .text h2 {
    font-size: 32px;
  }
}
@media (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .right .text h2 {
    font-size: 28px;
  }
}
.edoc-about-us .edoc-about-us-content .right .text h6 {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .right .text h6 {
    font-size: 14px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .right .text h6 {
    font-size: 16px;
  }
}
.edoc-about-us .edoc-about-us-content .right .learn-more-btn {
  margin-top: 40px;
}
@media (max-width: 767px) {
  .edoc-about-us .edoc-about-us-content .right .learn-more-btn {
    margin-top: 10px;
  }
}

.edoc-about-area {
  margin-top: 50px;
}
.edoc-about-area .about-area-content .left .about-img {
  width: 100%;
  height: 100%;
  padding-right: 90px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-about-area .about-area-content .left .about-img {
    padding-right: 0px;
  }
}
.edoc-about-area .about-area-content .left .about-img img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.edoc-about-area .about-area-content .left .who-we-are {
  margin-top: 30px;
}
.edoc-about-area .about-area-content .left .who-we-are p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
  margin-left: 55px;
}
.edoc-about-area .about-area-content .right {
  margin-top: 50px;
}
@media (max-width: 767px) {
  .edoc-about-area .about-area-content .right {
    margin-top: 0px;
  }
}
.edoc-about-area .about-area-content .right .edoc-title {
  color: var(--edc-heading);
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-about-area .about-area-content .right .edoc-title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-about-area .about-area-content .right .edoc-title {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .edoc-about-area .about-area-content .right .edoc-title {
    font-size: 24px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-area .about-area-content .right .edoc-title {
    font-size: 26px;
  }
}
.edoc-about-area .about-area-content .right .edoc-subtitle {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
}
.edoc-about-area .about-area-content .right .edc-progressbar-box {
  margin-top: 30px;
}
.edoc-about-area .about-area-content .right .action-btn {
  margin-top: 40px;
}
.edoc-about-area .about-area-content .right .company-stats {
  margin-top: 40px;
}
.edoc-about-area .about-area-content .right .company-stats .company-stats-box-col .company-stats-box {
  padding: 40px;
}
.edoc-about-area .about-area-content .right .company-stats .company-stats-box-col .company-stats-box h2 {
  color: var(--td-white);
  text-align: center;
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 16px;
}
.edoc-about-area .about-area-content .right .company-stats .company-stats-box-col .company-stats-box p {
  color: var(--td-white);
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(1) .company-stats-box {
  background-color: var(--edc-primary);
  border-radius: 16px 0px 0px 0px;
}
@media (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(1) .company-stats-box {
    border-radius: 16px 16px 0px 0px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(1) .company-stats-box {
    border-radius: 16px 0px 0px 0px;
  }
}
.edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(2) .company-stats-box {
  background-color: var(--edc-heading);
  border-radius: 0px 16px 0px 0px;
}
@media (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(2) .company-stats-box {
    border-radius: 0px 0px 0px 0px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(2) .company-stats-box {
    border-radius: 0px 16px 0px 0px;
  }
}
.edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(3) .company-stats-box {
  background-color: var(--edc-heading);
  border-radius: 0px 0px 0px 16px;
}
@media (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(3) .company-stats-box {
    border-radius: 0px 0px 0px 0px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(3) .company-stats-box {
    border-radius: 0px 0px 0px 16px;
  }
}
.edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(4) .company-stats-box {
  background-color: var(--edc-primary);
  border-radius: 0px 0px 16px 0px;
}
@media (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(4) .company-stats-box {
    border-radius: 0px 0px 16px 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-about-area .about-area-content .right .company-stats .company-stats-box-col:nth-child(4) .company-stats-box {
    border-radius: 0px 0px 16px 0px;
  }
}

/*----------------------------------------*/
/*  why choose us
/*----------------------------------------*/
.edoc-why-choose-us {
  background-color: var(--edc-bg-1);
}

/*----------------------------------------*/
/*  appointment
/*----------------------------------------*/
.edoc-appointment {
  background: rgba(7, 187, 168, 0.1);
  position: relative;
}
.edoc-appointment .appointment-form {
  position: relative;
  z-index: 5;
}
.edoc-appointment .appointment-form .appointment-box {
  background-color: var(--edc-heading);
  padding: 30px;
  border-radius: 16px;
}
@media (max-width: 767px) {
  .edoc-appointment .appointment-form .appointment-box {
    padding: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-appointment .appointment-form .appointment-box {
    padding: 30px;
  }
}
.edoc-appointment .appointment-form .appointment-box .action-btn {
  display: flex;
  justify-content: flex-end;
}
.edoc-appointment .appointment-form .right {
  position: relative;
}
.edoc-appointment .appointment-form .right .img-box {
  width: 100%;
  height: 516px;
  position: relative;
  z-index: 5;
}
@media (max-width: 767px) {
  .edoc-appointment .appointment-form .right .img-box {
    height: 235px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-appointment .appointment-form .right .img-box {
    height: 400px;
  }
}
.edoc-appointment .appointment-form .right .img-box img {
  height: 100%;
  width: 100%;
}
.edoc-appointment .appointment-form .right .text {
  position: absolute;
  top: 0%;
  left: 0%;
}
.edoc-appointment .appointment-form .right .text img {
  width: 100%;
}
.edoc-appointment .element {
  position: absolute;
  bottom: clamp(1.875rem, 6.5vw + 1rem, 6.25rem);
  right: 122px;
  z-index: 4;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-appointment .element {
    display: none;
  }
}

/*----------------------------------------*/
/*  faq
/*----------------------------------------*/
.edoc-faq .left {
  position: relative;
  margin-right: 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-faq .left {
    margin-right: 0px;
  }
}
.edoc-faq .left .img-box {
  width: 100%;
  height: 504px;
}
@media (max-width: 767px) {
  .edoc-faq .left .img-box {
    height: 304px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-faq .left .img-box {
    height: 404px;
  }
}
.edoc-faq .left .img-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 30px;
}
.edoc-faq .left .contact-box {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: var(--edc-primary);
  padding: 16px;
  border-radius: 0 16px 0 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}
.edoc-faq .left .contact-box .left-g {
  display: flex;
}
.edoc-faq .left .contact-box .left-g .phone-call {
  font-size: 30px;
  color: var(--td-white);
}
.edoc-faq .left .contact-box .right h6 {
  color: var(--td-white);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 8px;
}
.edoc-faq .left .contact-box .right p {
  color: var(--td-white);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.edoc-faq .right .text h2 {
  color: var(--edc-heading);
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .edoc-faq .right .text h2 {
    font-size: 42px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-faq .right .text h2 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-faq .right .text h2 {
    font-size: 32px;
  }
}
@media (max-width: 767px) {
  .edoc-faq .right .text h2 {
    font-size: 28px;
  }
}

.edc-faq-box {
  margin-right: 50px;
}
.edc-faq-box .faq-full-box {
  margin-bottom: 32px;
}
.edc-faq-box .faq-full-box .question-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.edc-faq-box .faq-full-box .question-box .left {
  display: flex;
  align-items: center;
  gap: 10px;
}
.edc-faq-box .faq-full-box .question-box .left .number {
  display: flex;
  width: 30px;
  height: 30px;
  padding: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 137px;
  background: var(--edc-primary);
  color: var(--td-white);
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edc-faq-box .faq-full-box .question-box .left .number {
    width: 25px;
    height: 25px;
    font-size: 16px;
    font-weight: 500;
  }
}
.edc-faq-box .faq-full-box .question-box .left h3 {
  color: var(--edc-heading);
  text-align: left;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edc-faq-box .faq-full-box .question-box .left h3 {
    font-size: 18px;
  }
}
.edc-faq-box .faq-full-box .question-box .right .plus {
  transition: transform 0.3s ease-in-out;
}
.edc-faq-box .faq-full-box .question-box .right .plus.active {
  transform: rotate(45deg);
}
.edc-faq-box .faq-full-box .answer-box {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--edc-text-primary);
  margin-left: 40px;
  margin-top: 30px;
  position: relative;
}
.edc-faq-box .faq-full-box .answer-box p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
}
.edc-faq-box .faq-full-box .answer-box .heighlight {
  position: absolute;
  top: -18px;
  right: -38px;
}

/*----------------------------------------*/
/*  ads
/*----------------------------------------*/
.edoc-adds-banner {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 24px;
}
.edoc-adds-banner .right .doctor-img {
  width: 100%;
  height: 527px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .edoc-adds-banner .right .doctor-img {
    height: 430px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-adds-banner .right .doctor-img {
    height: 360px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-adds-banner .right .doctor-img {
    height: 330px;
  }
}
@media (max-width: 767px) {
  .edoc-adds-banner .right .doctor-img {
    height: 100%;
  }
}
.edoc-adds-banner .right .doctor-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*----------------------------------------*/
/*  Service
/*----------------------------------------*/
.edoc-our-service-details .edoc-our-services-details-content .left {
  margin-right: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-service-details .edoc-our-services-details-content .left {
    margin-right: 0px;
  }
}
.edoc-our-service-details .edoc-our-services-details-content .left .img-box {
  width: 100%;
  height: 326px;
}
@media (max-width: 767px) {
  .edoc-our-service-details .edoc-our-services-details-content .left .img-box {
    height: 200px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-our-service-details .edoc-our-services-details-content .left .img-box {
    height: 326px;
  }
}
.edoc-our-service-details .edoc-our-services-details-content .left .img-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}

.edoc-service-about .edoc-service-about-content .left {
  margin-right: 20px;
}
.edoc-service-about .edoc-service-about-content .left h4 {
  color: var(--edc-heading);
  font-size: 20px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 30px;
}
.edoc-service-about .edoc-service-about-content .left p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
}
.edoc-service-about .edoc-service-about-content .right h4 {
  color: var(--edc-heading);
  font-size: 20px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 30px;
}
.edoc-service-about .edoc-service-about-content .right ul {
  list-style-type: none;
}
.edoc-service-about .edoc-service-about-content .right ul li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}
.edoc-service-about .edoc-service-about-content .right ul li:last-child {
  margin-bottom: 0;
}
.edoc-service-about .edoc-service-about-content .right ul li .icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: var(--edc-primary);
}
.edoc-service-about .edoc-service-about-content .right ul li .icon .check-icon {
  color: var(--td-white);
  font-size: 14px;
}
.edoc-service-about .edoc-service-about-content .right ul li span {
  color: #093E63;
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}

.edoc-service-points .edoc-service-points-content h4 {
  color: var(--edc-heading);
  font-size: 20px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 30px;
}

.edoc-our-services-2 {
  background-color: #F9F9F9;
}

/*----------------------------------------*/
/*  doctor details
/*----------------------------------------*/
.edoc-our-doctor-details {
  position: relative;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left {
  position: relative;
  z-index: 5;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(9, 62, 99, 0.1);
}
@media (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details {
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 10px;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .left h4 {
  color: var(--edc-heading);
  font-size: 30px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 10px;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .left p {
  color: var(--edc-primary);
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .right {
  padding: 16px;
  border-radius: 16px;
  border: 1px solid #F3F3F3;
  margin-right: 110px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .right {
    margin-right: 0px;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .right ul {
  list-style-type: none;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .right ul li {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 16px;
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .right ul li:last-child {
  margin-bottom: 0;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-details .right ul li span {
  color: var(--edc-heading);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .about-doctor {
  margin-top: 30px;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .about-doctor p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-education {
  margin-top: 30px;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-education .education-table {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-education .education-table .full-table {
  width: 100%;
  min-width: 600px;
  border-collapse: collapse;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-education .education-table .full-table .table-light {
  --bs-table-color: #093E63;
  --bs-table-bg: #F2FAFF;
  --bs-table-border-color: #F2FAFF;
  --bs-table-striped-bg: #F2FAFF;
  --bs-table-striped-color: #093E63;
  --bs-table-active-bg: #F2FAFF;
  --bs-table-active-color: #093E63;
  --bs-table-hover-bg: #F2FAFF;
  --bs-table-hover-color: #093E63;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-education .education-table .table > :not(caption) > * > * {
  padding: 10px 10px;
  color: #093E63;
  background-color: var(--bs-table-bg);
  border-bottom-width: none;
  box-shadow: none;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-education .education-table .table tbody tr td {
  color: #6B8BA1;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-schedule {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-schedule .schedule-table {
  width: 100%;
  min-width: 700px;
  border-collapse: collapse;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-schedule .schedule-table .schedule-table-full .table {
  --bs-table-border-color: rgba(9, 62, 99, 0.10);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: #093E63;
  border-color: rgba(9, 62, 99, 0.1);
  --bs-table-color: #6B8BA1;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-schedule .schedule-table .schedule-table-full .table > :not(caption) > * > * {
  padding: 10px 10px;
  color: #093E63;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .left .doctor-schedule .schedule-table .schedule-table-full .table tbody tr td {
  color: #6B8BA1;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right {
  margin-left: 50px;
  position: relative;
  z-index: 5;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .right {
    margin-left: 0px;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box {
  position: relative;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .pro {
  position: absolute;
  top: 0;
  left: -25%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .pro {
    left: -5%;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .pro img {
  width: 178px;
  height: 178px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .pro img {
    width: 100px;
    height: 100px;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .call {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -25%;
}
@media only screen and (min-width: 1400px) and (max-width: 1769px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .call {
    right: 0%;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .call a {
  display: flex;
  align-items: center;
  gap: 10px;
  border-radius: 16px;
  background: #FFF;
  box-shadow: 0px 12px 56px 0px rgba(6, 28, 61, 0.12);
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  text-decoration: none;
  gap: 15px;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .call a .icon {
  font-size: 30px;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .call a .texts .title {
  display: block;
  color: #093E63;
  font-size: 18px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 8px;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .call a .texts .subtitle {
  display: block;
  color: #093E63;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .doctor-img {
  width: 100%;
  height: 490px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .doctor-img {
    height: 380px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .doctor-img {
    height: 100%;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .doctor-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .follow-box {
  background-color: var(--edc-heading);
  padding: 30px;
  border-radius: 0 0 16px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .follow-box {
    flex-direction: column;
    gap: 20px;
  }
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .follow-box h5 {
  color: var(--td-white);
  font-size: 20px;
  font-weight: 500;
  line-height: normal;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .follow-box .follow-links {
  display: flex;
  align-items: center;
  gap: 10px;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .follow-box .follow-links a {
  display: inline-flex;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 66px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.03);
  transition: all 0.3s ease-in-out;
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .follow-box .follow-links a .social-icon {
  font-size: 20px;
  color: var(--td-white);
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .doctor-box .follow-box .follow-links a:hover {
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
}
.edoc-our-doctor-details .edoc-our-doctor-details-content .right .skills {
  margin-top: 30px;
  padding: 30px;
  border-radius: 16px;
  border: 1px solid rgba(34, 34, 34, 0.1);
}
.edoc-our-doctor-details .details-element {
  position: absolute;
  top: 40px;
  right: 40px;
  z-index: 1;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-our-doctor-details .details-element {
    display: none;
  }
}

/*----------------------------------------*/
/*  Blog details
/*----------------------------------------*/
.blog-details .left {
  margin-right: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .blog-details .left {
    margin-right: 0px;
  }
}
.blog-details .left .blog-details-banner {
  width: 100%;
  height: 438px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-details .left .blog-details-banner {
    height: 380px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-details .left .blog-details-banner {
    height: 350px;
  }
}
@media (max-width: 767px) {
  .blog-details .left .blog-details-banner {
    height: 250px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .blog-details .left .blog-details-banner {
    height: 300px;
  }
}
.blog-details .left .blog-details-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}
.blog-details .left .blog-amenities {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 16px;
}
@media (max-width: 767px) {
  .blog-details .left .blog-amenities {
    flex-direction: column;
    align-items: start;
  }
}
.blog-details .left .blog-amenities .post-by {
  display: flex;
  align-items: center;
  gap: 8px;
}
.blog-details .left .blog-amenities .post-by .icon {
  display: flex;
}
.blog-details .left .blog-amenities .post-by .icon .post-icon {
  font-size: 16px;
  color: var(--edc-text-primary);
}
.blog-details .left .blog-amenities .post-by p {
  color: var(--edc-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
.blog-details .left .blog-des {
  margin-top: 30px;
}
.blog-details .left .blog-des h2 {
  color: var(--edc-heading);
  font-size: 30px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .blog-details .left .blog-des h2 {
    font-size: 26px;
  }
}
.blog-details .left .blog-des p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
}
.blog-details .left .blog-des h3 {
  color: var(--edc-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 20px;
}
.blog-details .left .blog-des ul {
  margin-top: 10px;
  margin-bottom: 10px;
}
.blog-details .left .blog-des ul li {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
  margin-bottom: 5px;
}
.blog-details .left .blog-des ul li:last-child {
  margin-bottom: 0;
}
.blog-details .left .blog-des ul li span {
  color: var(--edc-heading);
  font-weight: 500;
}
.blog-details .left hr {
  border-color: #093E63;
  opacity: 0.1;
  border-width: 1px;
}
.blog-details .left .share {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}
.blog-details .left .share h5 {
  color: var(--edc-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 1.3;
}
.blog-details .left .share .social-links {
  display: flex;
  align-items: center;
  gap: 10px;
}
.blog-details .left .share .social-links a {
  display: inline-flex;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 50%;
  border: 1px solid var(--edc-text-primary);
  transition: all 0.3s ease-in-out;
}
.blog-details .left .share .social-links a .social-icon {
  font-size: 20px;
  color: var(--edc-text-primary);
}
.blog-details .left .share .social-links a:hover {
  border: 1px solid var(--edc-heading);
  background: var(--edc-heading);
}
.blog-details .left .share .social-links a:hover .social-icon {
  color: var(--td-white);
}
.blog-details .right .right-card {
  padding: 24px;
  border-radius: 24px;
  background: var(--edc-heading);
  margin-bottom: 48px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .blog-details .right .right-card {
    margin-bottom: 20px;
  }
}
.blog-details .right .right-card:last-child {
  margin-bottom: 0;
}
.blog-details .right .right-card h4 {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(7, 187, 168, 0.04);
  background: rgba(255, 255, 255, 0.04);
  color: var(--td-white);
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 16px;
}
.blog-details .right .right-card ul li {
  margin-bottom: 16px;
}
.blog-details .right .right-card ul li:last-child {
  margin-bottom: 0;
}
.blog-details .right .right-card ul li a {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  font-weight: 500;
  line-height: 1.75;
  transition: all 0.3s ease-in-out;
}
.blog-details .right .right-card ul li a .icon {
  display: flex;
  align-items: center;
  justify-content: center;
}
.blog-details .right .right-card ul li a .icon .check-icon {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
}
.blog-details .right .right-card ul li a:hover {
  color: var(--td-white);
}
.blog-details .right .right-card ul li a:hover .icon .check-icon {
  color: var(--td-white);
}

/*----------------------------------------*/
/*  Contact
/*----------------------------------------*/
.edoc-contact-page .edoc-contact-page-content {
  position: relative;
  z-index: 5;
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card {
  padding: 30px;
  border-radius: 30px;
  border: 1px solid rgba(34, 34, 34, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card .icon {
  display: flex;
  width: 60px;
  height: 60px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  background: var(--edc-primary);
  border-radius: 8px;
  margin-bottom: 20px;
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card .icon .location-icon {
  font-size: 30px;
  color: var(--td-white);
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card .text {
  text-align: center;
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card .text h6 {
  color: var(--edc-heading);
  font-size: 20px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 16px;
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card .text p {
  color: var(--edc-text-primary);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
  margin-bottom: 40px;
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card .text a {
  color: var(--edc-primary);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: 6.5%;
  text-underline-offset: 14%;
  text-underline-position: from-font;
  transition: all 0.3s ease-in-out;
}
.edoc-contact-page .edoc-contact-page-content .contact-details .contact-details-card .text a:hover {
  color: var(--edc-heading);
}

.contact-page-forms {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.contact-page-forms .contact-page-form-content .left h2 {
  color: var(--edc-heading);
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .contact-page-forms .contact-page-form-content .left h2 {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .contact-page-forms .contact-page-form-content .left h2 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-page-forms .contact-page-form-content .left h2 {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .contact-page-forms .contact-page-form-content .left h2 {
    font-size: 26px;
  }
}
.contact-page-forms .contact-page-form-content .left p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
  margin-bottom: 50px;
}
.contact-page-forms .contact-page-form-content .left .action-btn {
  display: flex;
  justify-content: flex-end;
}

.edoc-google-location .iframe iframe {
  width: 100%;
  height: 672px;
  border: none;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .edoc-google-location .iframe iframe {
    height: 600px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-google-location .iframe iframe {
    height: 550px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-google-location .iframe iframe {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .edoc-google-location .iframe iframe {
    height: 450px;
  }
}

/*----------------------------------------*/
/*  5.1 footer
/*----------------------------------------*/
.edoc-footer {
  background-color: var(--edc-heading);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding-top: 100px;
  padding-bottom: 30px;
  position: relative;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .edoc-footer {
    padding-top: 80px;
    padding-bottom: 25px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-footer {
    padding-top: 70px;
    padding-bottom: 20px;
  }
}
@media (max-width: 767px) {
  .edoc-footer {
    padding-top: 50px;
    padding-bottom: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-footer {
    padding-top: 70px;
    padding-bottom: 20px;
  }
}
.edoc-footer .edoc-footer-top {
  margin-bottom: 80px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-footer .edoc-footer-top {
    margin-bottom: 50px;
  }
}
@media (max-width: 767px) {
  .edoc-footer .edoc-footer-top {
    margin-bottom: 40px;
  }
}
@media (max-width: 767px) {
  .edoc-footer .edoc-footer-top .edoc-foot-1 {
    margin-bottom: 10px;
  }
}
.edoc-footer .edoc-footer-top .edoc-foot-1 .foot-logo {
  display: inline-block;
  height: 30px;
  margin-bottom: 16px;
}
.edoc-footer .edoc-footer-top .edoc-foot-1 .foot-logo img {
  height: 100%;
}
.edoc-footer .edoc-footer-top .edoc-foot-1 .foot-des {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
  margin-bottom: 40px;
  width: 78%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-footer .edoc-footer-top .edoc-foot-1 .foot-des {
    width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-footer .edoc-footer-top .edoc-foot-1 .foot-des {
    margin-bottom: 20px;
  }
}
.edoc-footer .edoc-footer-top .edoc-foot-1 .links {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-footer .edoc-footer-top .edoc-foot-1 .links {
    margin-bottom: 10px;
  }
}
.edoc-footer .edoc-footer-top .edoc-foot-1 .links:last-child {
  margin-bottom: 0;
}
.edoc-footer .edoc-footer-top .edoc-foot-1 .links .social-link {
  font-size: 20px;
  color: var(--edc-text-primary);
}
.edoc-footer .edoc-footer-top .edoc-foot-1 .links p {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.edoc-footer .edoc-footer-top .edoc-foot-4 {
  margin-left: 80px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .edoc-footer .edoc-footer-top .edoc-foot-4 {
    margin-left: 0px;
    margin-top: 50px;
  }
}
@media (max-width: 767px) {
  .edoc-footer .edoc-footer-top .edoc-foot-4 {
    margin-left: 0px;
    margin-top: 10px;
  }
}
.edoc-footer .edoc-footer-top .footer-common-links h4 {
  color: var(--td-white);
  font-size: 20px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 20px;
}
.edoc-footer .edoc-footer-top .footer-common-links .footer-common-links-des {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
  width: 78%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-footer .edoc-footer-top .footer-common-links .footer-common-links-des {
    width: 100%;
  }
}
.edoc-footer .edoc-footer-top .footer-common-links ul {
  list-style-type: none;
}
.edoc-footer .edoc-footer-top .footer-common-links ul li {
  margin-bottom: 16px;
}
.edoc-footer .edoc-footer-top .footer-common-links ul li:last-child {
  margin-bottom: 0;
}
.edoc-footer .edoc-footer-top .footer-common-links ul li a {
  color: var(--edc-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.edoc-footer .edoc-footer-top .footer-common-links ul li a:hover {
  color: var(--edc-primary);
}
.edoc-footer .edoc-footer-top .footer-common-links .phone-call-btn {
  display: flex;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--edc-primary);
  margin-top: 30px;
  align-items: center;
  gap: 16px;
}
.edoc-footer .edoc-footer-top .footer-common-links .phone-call-btn .icon {
  display: inline-flex;
  width: 30px;
  height: 30px;
  justify-content: center;
  align-items: center;
}
.edoc-footer .edoc-footer-top .footer-common-links .phone-call-btn .icon .call-icon {
  font-size: 30px;
  color: var(--td-white);
}
.edoc-footer .edoc-footer-top .footer-common-links .phone-call-btn .texts {
  display: inline-flex;
  flex-direction: column;
}
.edoc-footer .edoc-footer-top .footer-common-links .phone-call-btn .texts .title {
  display: inline-block;
  color: var(--td-white);
  font-size: 18px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 8px;
}
.edoc-footer .edoc-footer-top .footer-common-links .phone-call-btn .texts .subtitle {
  display: inline-block;
  color: var(--td-white);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
.edoc-footer .edoc-footer-bottom {
  padding-top: 14px;
  border-top: 1px solid rgba(107, 139, 161, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom {
    flex-direction: column;
    justify-content: start;
    align-items: start;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
.edoc-footer .edoc-footer-bottom .left p {
  color: var(--edc-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
.edoc-footer .edoc-footer-bottom .right {
  display: flex;
  align-items: center;
  gap: 16px;
}
@media (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom .right {
    margin-top: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom .right {
    margin-top: 0px;
  }
}
.edoc-footer .edoc-footer-bottom .right a {
  display: inline-flex;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 50%;
  border: 1px solid var(--edc-text-primary);
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom .right a {
    width: 30px;
    height: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom .right a {
    width: 40px;
    height: 40px;
  }
}
.edoc-footer .edoc-footer-bottom .right a .social-icon {
  font-size: 20px;
  color: var(--edc-text-primary);
}
@media (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom .right a .social-icon {
    font-size: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .edoc-footer .edoc-footer-bottom .right a .social-icon {
    font-size: 20px;
  }
}
.edoc-footer .edoc-footer-bottom .right a:hover {
  background-color: var(--edc-text-primary);
}
.edoc-footer .edoc-footer-bottom .right a:hover .social-icon {
  color: var(--td-white);
}
.edoc-footer .edoc-footer-element {
  position: absolute;
  bottom: 0;
  right: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .edoc-footer .edoc-footer-element {
    display: none;
  }
}

/*# sourceMappingURL=styles.css.map */
