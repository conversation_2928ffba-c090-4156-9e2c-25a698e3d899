@use '../../utils' as *;

/*----------------------------------------*/
/*  nice select
/*----------------------------------------*/
.edc-custom-nice-select {
  .nice-select {
    -webkit-tap-highlight-color: transparent;
    background-color: transparent;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.10);
    font-size: 13px;
    font-weight: 400;
    height: 50px;
    line-height: 49px;
    color: var(--td-white);
    width: 100%;
    padding-left: 15px;
  }

  .nice-select:after {
    border-bottom: 1px solid #9DB2C1;
    border-right: 1px solid #9DB2C1;
    right: 15px;
    height: 7px;
    width: 7px;
  }

  .nice-select .list {
    background-color: #062A44;
    width: 100%;
    border-radius: 7px;
    color: #6B8BA1;
    font-size: 14px;
  }

  .nice-select .option:hover,
  .nice-select .option.focus,
  .nice-select .option.selected.focus {
    background-color: #083555;
    font-weight: 400;
    color: #fff;
    font-size: 14px;
  }

  &-2 {
    .nice-select {
      border: 1px solid rgba(34, 34, 34, 0.10);
      color: #093E63;

      &.open{
        border: 1px solid var(--edc-primary);
      }
    }

    .nice-select .list {
    background-color: #f0f0f0;
    width: 100%;
    border-radius: 7px;
    color: #093E63;
    font-size: 14px;
  }

  .nice-select .option:hover,
  .nice-select .option.focus,
  .nice-select .option.selected.focus {
    background-color: #dbdbdb;
    font-weight: 400;
    color: #093E63;
    font-size: 14px;
  }
  }
}