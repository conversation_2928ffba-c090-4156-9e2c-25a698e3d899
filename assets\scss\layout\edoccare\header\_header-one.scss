@use '../../../utils' as *;

/*----------------------------------------*/
/*  3.1 Header-1
/*----------------------------------------*/
.edc-header {
  .header-top {
    background-color: #CEFFFA;

    .header-top-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        .logo {
          a {
            display: block;
            height: 32px;

            img {
              height: 100%;
              width: 100%;
            }
          }
        }
      }

      .right {
        display: flex;
        align-items: center;

        .header-top-right-card {
          display: flex;
          align-items: center;
          border-right: 2px solid #A8FFF6;
          padding: 20px 60px;
          gap: 16px;

          @media #{$xl} {
            padding: 16px 40px;
          }

          @media #{$lg,$md,$xs} {
            padding: 14px 36px;
          }

          &:first-child {
            padding-left: 0;
          }

          .icon {
            display: flex;

            .header-top-icon {
              font-size: 24px;
              color: var(--edc-primary);
            }
          }

          .text {
            h6 {
              font-size: 14px;
              font-weight: 500;
              color: var(--edc-heading);
              margin-bottom: 4px;
            }

            p {
              font-size: 14px;
              font-weight: 400;
              color: var(--edc-text-primary);
            }
          }
        }
      }
    }


  }

  .main-header {
    background-color: var(--edc-heading);
    padding: 18px 0;

    .main-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        .logo {
          height: 32px;

          @media #{$xs} {
            height: 26px;
          }

          img {
            height: 100%;
            width: 100%;
          }
        }

        .menu {
          ul {
            list-style-type: none;
            display: flex;
            gap: 30px;

            li {
              a {
                color: var(--td-white);
                font-size: 16px;
                font-weight: 300;
                line-height: normal;
                transition: all 0.3s ease-in-out;

                &:hover,
                &.active {
                  color: var(--edc-primary);
                }
              }
            }
          }
        }
      }

      .right {
        display: flex;
        align-items: center;
        gap: 10px;

        @media #{$xs} {
          gap: 6px;
        }

        .action-btn {
          @media #{$xs} {
            display: none;
          }

          @media #{$sm} {
            display: block;
          }
        }

        .toggle-btn {
          .edoc-toggle-btn {
            display: flex;

            .menu-icon {
              font-size: 30px;
              color: var(--td-white);

              @media #{$xs} {
                font-size: 24px;
              }
            }
          }
        }
      }
    }
  }
}