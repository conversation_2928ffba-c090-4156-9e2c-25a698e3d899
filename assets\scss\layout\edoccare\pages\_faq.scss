@use '../../../utils' as *;

/*----------------------------------------*/
/*  faq
/*----------------------------------------*/
.edoc-faq {
  .left {
    position: relative;
    margin-right: 30px;

    @media #{$lg,$md,$xs} {
      margin-right: 0px;
    }

    .img-box {
      width: 100%;
      height: 504px;

      @media #{$xs} {
        height: 304px;
      }
      @media #{$sm} {
        height: 404px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 30px;
      }
    }

    .contact-box {
      position: absolute;
      bottom: 0;
      left: 0;
      background-color: var(--edc-primary);
      padding: 16px;
      border-radius: 0 16px 0 16px;
      display: flex;
      align-items: center;
      gap: 16px;

      .left-g {
        display: flex;

        .phone-call {
          font-size: 30px;
          color: var(--td-white);
        }
      }

      .right {
        h6 {
          color: var(--td-white);
          font-size: 18px;
          font-weight: 600;
          line-height: normal;
          margin-bottom: 8px;
        }

        p {
          color: var(--td-white);
          font-size: 16px;
          font-weight: 400;
          line-height: normal;
        }


      }
    }
  }

  .right {
    .text {
      h2 {
        color: var(--edc-heading);
        font-size: 48px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 16px;

        @media #{$xl} {
          font-size: 42px;
        }

        @media #{$lg} {
          font-size: 36px;
        }

        @media #{$md} {
          font-size: 32px;
        }

        @media #{$xs} {
          font-size: 28px;
        }
      }
    }
  }
}

.edc-faq-box {
  margin-right: 50px;

  .faq-full-box {
    margin-bottom: 32px;

    .question-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;

      .left {
        display: flex;
        align-items: center;
        gap: 10px;

        .number {
          display: flex;
          width: 30px;
          height: 30px;
          padding: 10px;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 137px;
          background: var(--edc-primary);
          color: var(--td-white);
          text-align: center;
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;

          @media #{$lg,$md,$xs} {
            width: 25px;
            height: 25px;
            font-size: 16px;
            font-weight: 500;
          }
        }

        h3 {
          color: var(--edc-heading);
          text-align: left;
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;

          @media #{$lg,$md,$xs} {
            font-size: 18px;
          }
        }
      }

      .right {
        .plus {
          transition: transform 0.3s ease-in-out;

          &.active {
            transform: rotate(45deg);
          }
        }
      }
    }

    .answer-box {
      padding: 16px;
      border-radius: 8px;
      border: 1px solid var(--edc-text-primary);
      margin-left: 40px;
      margin-top: 30px;
      position: relative;

      p {
        color: var(--edc-text-primary);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(26, 16);
      }

      .heighlight {
        position: absolute;
        top: -18px;
        right: -38px;
      }
    }
  }
}