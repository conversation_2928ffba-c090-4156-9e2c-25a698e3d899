@use '../../utils' as *;

/*----------------------------------------*/
/*  Progress bar
/*----------------------------------------*/
.edc-progressbar-box {
  .edc-progress-box {
    .p-title {
      color: var(--td-white);
      font-size: 16px;
      font-weight: 600;
      line-height: 26px;
      display: block;
      margin-bottom: 5px;
    }

    .edc-progress {
      height: 10px;
      background: #EEEEEE;
      border-radius: 20px;
      overflow: visible;
      margin-bottom: 50px;
      position: relative;

      .edc-progressbar {
        position: relative;
        border-radius: 20px;
        animation: animate-positive 2s;
        height: 100%;
      }

      .edc-progressvalue {
        position: absolute;
        bottom: -40px;
        right: 0;
        transform: translateX(47%);
        padding: 5px 14px;
        border-radius: 4px;
        background: #FFF;
        color: var(--edc-heading);
        text-align: center;
        z-index: 10;
        min-width: 64px;
        color: var(--edc-heading);
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 18px;

        &:after {
          content: "";
          position: absolute;
          top: -8px;
          left: 50%;
          transform: translateX(-50%) rotate(180deg);
          width: 16px;
          height: 16px;
          background: #FFF;
          clip-path: polygon(0% 0%, 100% 0%, 50% 100%);
        }
      }
    }

    &-2 {
      .p-title {
        color: var(--td-heading);
        font-size: 14px;
        font-weight: 600;
      }

      .edc-progress {
        .edc-progressvalue {
          background: var(--edc-heading);
          color: var(--td-white);

          &::after {
            background: var(--edc-heading);
          }
        }
      }
    }
  }

  @keyframes animate-positive {
    0% {
      width: 0%;
    }
  }
}