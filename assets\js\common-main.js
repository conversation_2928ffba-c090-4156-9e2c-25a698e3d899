(function ($) {
  'use strict';

  // Data Css js
  $("[data-background").each(function () {
    $(this).css(
      "background-image",
      "url( " + $(this).attr("data-background") + "  )"
    );
  });

  $("[data-width]").each(function () {
    $(this).css("width", $(this).attr("data-width"));
  });

  $("[data-bg-color]").each(function () {
    $(this).css("background-color", $(this).attr("data-bg-color"));
  });

  // video popup
  $('.popup-video').magnificPopup({
    type: 'iframe'
    // other options
  });

  // image popup
  $('.popup-image').magnificPopup({
    type: 'image',
    // other options
    gallery: {
      enabled: true,
      navigateByImgClick: true,
      preload: [0, 1]
    },
  });

  // jarallax
  if ($('.jarallax').length) {
    $('.jarallax').jarallax({
      speed: 0.2,
    });
  }


})(jQuery);