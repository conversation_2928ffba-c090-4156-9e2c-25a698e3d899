@use '../../utils' as *;

/*----------------------------------------*/
/*  Button
/*----------------------------------------*/
.edoc-primary-btn {
  all: unset;
  display: inline-flex;
  height: 44px;
  align-items: center;
  position: relative;
  padding: 0 24px;
  border: #07BBA8 solid 1px;
  border-radius: 4px;
  color: var(--td-white);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  overflow: hidden;
  transition: border 700ms, color 600ms;
  user-select: none;

  @media #{$xs} {
    height: 38px;
    font-size: 12px;
  }

  span {
    display: block;
    z-index: 1;
  }

  &:hover,
  &.active {
    color: #fff;
  }

  &::after {
    content: "";
    position: absolute;
    width: 7.5em;
    aspect-ratio: 1;
    background: var(--edc-ternary);
    opacity: 50%;
    border-radius: 50%;
    transition: transform 700ms, background 600ms;
    left: 0;
    transform: translateX(-6.7em);
  }

  &::before {
    content: "";
    position: absolute;
    width: 7.5em;
    aspect-ratio: 1;
    background: var(--edc-ternary);
    opacity: 50%;
    border-radius: 50%;
    transition: transform 700ms, background 600ms;
    right: 0;
    transform: translateX(6.7em);
  }

  &:hover::after {
    transform: translateX(-5px);
    background: var(--edc-primary);
    width: 14em;
  }

  &:hover::before {
    transform: translateX(5px);
    background: var(--edc-primary);
    width: 14em;
  }

  &.active::after {
    background: var(--edc-primary);
  }

  &.active::before {
    background: var(--edc-primary);
  }

  &.primary-color {
    color: var(--edc-primary);
    background: var(--td-white);
    padding: 0 32px;

    &:hover,
    &.active {
      color: #fff;
      z-index: 1;
    }
  }

  &.button-xl{
    height: 52px;
    font-size: 16px;

    @media #{$md,$xs} {
      height: 45px;
    }
    @media #{$xs} {
      height: 40px;
    }
  }
}